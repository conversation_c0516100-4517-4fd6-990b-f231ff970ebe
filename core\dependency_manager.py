"""
Dependency Management System for CBRE Validation Agent
Handles missing dependencies gracefully and provides installation guidance
"""

import sys
import subprocess
import importlib
from typing import Dict, List, Tuple, Optional

class DependencyManager:
    """Manages dependencies and provides graceful fallbacks"""
    
    REQUIRED_PACKAGES = {
        'streamlit': 'streamlit',
        'pandas': 'pandas',
        'openpyxl': 'openpyxl',
        'python-dotenv': 'dotenv',
        'pyyaml': 'yaml',
        'xlrd': 'xlrd'
    }
    
    OPTIONAL_PACKAGES = {
        'langchain': 'langchain',
        'langchain-openai': 'langchain_openai',
        'openai': 'openai'
    }
    
    def __init__(self):
        self.missing_required = []
        self.missing_optional = []
        self.available_packages = {}
        self._check_dependencies()
    
    def _check_dependencies(self):
        """Check which dependencies are available"""
        # Check required packages
        for package_name, import_name in self.REQUIRED_PACKAGES.items():
            try:
                module = importlib.import_module(import_name)
                self.available_packages[package_name] = module
            except ImportError:
                self.missing_required.append(package_name)
        
        # Check optional packages
        for package_name, import_name in self.OPTIONAL_PACKAGES.items():
            try:
                module = importlib.import_module(import_name)
                self.available_packages[package_name] = module
            except ImportError:
                self.missing_optional.append(package_name)
    
    def get_status(self) -> Dict:
        """Get dependency status"""
        return {
            "required_missing": self.missing_required,
            "optional_missing": self.missing_optional,
            "available": list(self.available_packages.keys()),
            "ai_features_available": len(self.missing_optional) == 0
        }
    
    def can_run_basic_features(self) -> bool:
        """Check if basic features can run"""
        return len(self.missing_required) == 0
    
    def can_run_ai_features(self) -> bool:
        """Check if AI features can run"""
        return 'langchain' in self.available_packages and 'openai' in self.available_packages
    
    def get_installation_command(self) -> str:
        """Get pip install command for missing packages"""
        all_missing = self.missing_required + self.missing_optional
        if not all_missing:
            return "All dependencies are installed!"
        
        return f"pip install {' '.join(all_missing)}"
    
    def install_missing_packages(self, include_optional: bool = True) -> Tuple[bool, str]:
        """Attempt to install missing packages"""
        packages_to_install = self.missing_required.copy()
        if include_optional:
            packages_to_install.extend(self.missing_optional)
        
        if not packages_to_install:
            return True, "All packages already installed"
        
        try:
            cmd = [sys.executable, "-m", "pip", "install"] + packages_to_install
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            # Re-check dependencies after installation
            self._check_dependencies()
            
            return True, f"Successfully installed: {', '.join(packages_to_install)}"
        
        except subprocess.CalledProcessError as e:
            return False, f"Installation failed: {e.stderr}"
        except Exception as e:
            return False, f"Installation error: {str(e)}"

# Global dependency manager instance
_dependency_manager = None

def get_dependency_manager() -> DependencyManager:
    """Get global dependency manager instance"""
    global _dependency_manager
    if _dependency_manager is None:
        _dependency_manager = DependencyManager()
    return _dependency_manager

def check_dependencies() -> Dict:
    """Quick dependency check"""
    return get_dependency_manager().get_status()

def safe_import_langchain():
    """Safely import langchain components with fallbacks"""
    try:
        from langchain.schema import SystemMessage, HumanMessage
        from langchain_openai import ChatOpenAI
        return {
            "available": True,
            "SystemMessage": SystemMessage,
            "HumanMessage": HumanMessage,
            "ChatOpenAI": ChatOpenAI
        }
    except ImportError as e:
        return {
            "available": False,
            "error": str(e),
            "SystemMessage": None,
            "HumanMessage": None,
            "ChatOpenAI": None
        }

def safe_import_openai():
    """Safely import OpenAI with fallbacks"""
    try:
        import openai
        return {
            "available": True,
            "openai": openai
        }
    except ImportError as e:
        return {
            "available": False,
            "error": str(e),
            "openai": None
        }

class MockLLM:
    """Mock LLM for when AI dependencies are not available"""
    
    def __init__(self, *args, **kwargs):
        self.model_name = "mock-llm"
    
    def __call__(self, messages):
        """Mock LLM call"""
        class MockResponse:
            def __init__(self):
                self.content = """
                {
                    "analysis": "AI dependencies not available - using mock response",
                    "success": false,
                    "error": "LangChain/OpenAI not installed",
                    "recommendation": "Install dependencies with: pip install langchain langchain-openai openai"
                }
                """
        return MockResponse()

def get_llm_with_fallback():
    """Get LLM with fallback to mock if dependencies missing"""
    langchain_imports = safe_import_langchain()
    
    if langchain_imports["available"]:
        try:
            from core.llm_config import get_llm
            return get_llm()
        except Exception as e:
            print(f"Warning: Could not initialize LLM: {e}")
            return MockLLM()
    else:
        print("Warning: LangChain not available, using mock LLM")
        return MockLLM()

def create_dependency_report() -> str:
    """Create a detailed dependency report"""
    dm = get_dependency_manager()
    status = dm.get_status()
    
    report = "🔍 CBRE Validation Agent - Dependency Report\n"
    report += "=" * 50 + "\n\n"
    
    if status["required_missing"]:
        report += "❌ MISSING REQUIRED DEPENDENCIES:\n"
        for pkg in status["required_missing"]:
            report += f"   - {pkg}\n"
        report += "\n"
    
    if status["optional_missing"]:
        report += "⚠️ MISSING OPTIONAL DEPENDENCIES (AI Features):\n"
        for pkg in status["optional_missing"]:
            report += f"   - {pkg}\n"
        report += "\n"
    
    if status["available"]:
        report += "✅ AVAILABLE DEPENDENCIES:\n"
        for pkg in status["available"]:
            report += f"   - {pkg}\n"
        report += "\n"
    
    report += f"🤖 AI Features Available: {'Yes' if status['ai_features_available'] else 'No'}\n"
    report += f"📊 Basic Features Available: {'Yes' if dm.can_run_basic_features() else 'No'}\n\n"
    
    if status["required_missing"] or status["optional_missing"]:
        report += "💡 INSTALLATION COMMAND:\n"
        report += f"   {dm.get_installation_command()}\n\n"
    
    return report
