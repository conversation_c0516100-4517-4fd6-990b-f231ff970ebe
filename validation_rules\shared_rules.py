import pandas as pd

def check_required_columns(df: pd.DataFrame, required_columns: list[str]) -> list[str]:
    missing = [col for col in required_columns if col not in df.columns]
    return missing

def check_column_data_types(df: pd.DataFrame, data_types: dict) -> list[str]:
    errors = []

    for col, expected_type in data_types.items():
        if col not in df.columns:
            continue  # already caught in required check

        if expected_type == "string":
            if not pd.api.types.is_string_dtype(df[col]):
                errors.append(f"{col} should be a string.")
        elif expected_type == "float":
            if not pd.api.types.is_float_dtype(df[col]) and not pd.api.types.is_numeric_dtype(df[col]):
                errors.append(f"{col} should be a float or numeric.")
        elif expected_type == "int":
            if not pd.api.types.is_integer_dtype(df[col]):
                errors.append(f"{col} should be an integer.")
    return errors
