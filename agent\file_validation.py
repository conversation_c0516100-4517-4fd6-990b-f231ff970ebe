import os
import pandas as pd
import json
from typing import Dict, List, Any
from core.ai_agent_base import AIAgentBase
from core.config_loader import get_paths_config, get_metadata_config

class FileValidationAgent(AIAgentBase):
    def __init__(self):
        super().__init__(
            agent_name="File Validation AI Agent",
            agent_role="Intelligent file format and structure analysis specialist"
        )
        self.paths_config = get_paths_config()
        self.metadata_config = get_metadata_config()
        self.supported_extensions = self.paths_config.get("supported_extensions", [".xlsx", ".xls"])
        self.max_file_size_mb = self.metadata_config.get("validation", {}).get("max_file_size_mb", 50)

    def analyze_file_with_ai(self, file_path: str, df: pd.DataFrame, file_info: Dict) -> Dict:
        """AI-powered comprehensive file analysis"""

        system_prompt = """
        You are an expert file validation specialist with deep knowledge of Excel file formats,
        financial data structures, and data quality assessment.

        Your expertise includes:
        1. Excel file format validation and compatibility
        2. Financial data file structure analysis
        3. Data integrity and completeness assessment
        4. File corruption detection
        5. Performance and usability evaluation

        Provide comprehensive file analysis in JSON format:
        {
            "file_quality_score": 0.0-1.0,
            "validation_passed": true/false,
            "format_analysis": {
                "excel_compatibility": "excellent|good|fair|poor",
                "structure_quality": "excellent|good|fair|poor",
                "format_issues": []
            },
            "content_analysis": {
                "data_completeness": 0.0-1.0,
                "structure_consistency": 0.0-1.0,
                "potential_issues": [],
                "data_patterns": []
            },
            "performance_analysis": {
                "file_size_assessment": "optimal|acceptable|large|excessive",
                "processing_complexity": "low|medium|high",
                "memory_requirements": "low|medium|high"
            },
            "recommendations": [],
            "detailed_assessment": "comprehensive analysis explanation",
            "readiness_for_processing": true/false
        }
        """

        # Create detailed file profile
        file_profile = {
            "basic_info": file_info,
            "content_summary": {
                "total_rows": len(df),
                "total_columns": len(df.columns),
                "column_names": list(df.columns),
                "data_types": {col: str(df[col].dtype) for col in df.columns},
                "memory_usage_mb": df.memory_usage(deep=True).sum() / (1024 * 1024)
            },
            "data_quality_indicators": {
                "null_percentages": {col: (df[col].isnull().sum() / len(df)) * 100 for col in df.columns},
                "unique_value_counts": {col: df[col].nunique() for col in df.columns},
                "sample_values": {col: df[col].dropna().head(3).tolist() for col in df.columns}
            }
        }

        user_prompt = f"""
        Perform comprehensive validation analysis for this Excel file:

        File Profile:
        {json.dumps(file_profile, indent=2, default=str)}

        Analysis Requirements:
        1. Validate Excel file format and compatibility
        2. Assess data structure and organization quality
        3. Evaluate data completeness and consistency
        4. Identify potential processing issues
        5. Assess file size and performance implications
        6. Provide recommendations for optimization
        7. Determine readiness for downstream processing

        Consider:
        - Is this a well-structured financial data file?
        - Are there any format or compatibility issues?
        - Is the data organized in a processable manner?
        - Are there any red flags or concerns?
        - What recommendations would improve file quality?
        """

        return self.analyze_with_ai(system_prompt, user_prompt, file_profile)

    def run(self, file_path: str) -> dict:
        """Main AI-powered file validation entry point"""
        try:
            # Basic file existence and format checks
            if not os.path.exists(file_path):
                return {
                    "success": False,
                    "message": f"File not found: {file_path}",
                    "ai_analysis": "File does not exist at specified path",
                    "agent_report": self.generate_detailed_report()
                }

            # File format validation
            _, ext = os.path.splitext(file_path)
            ext = ext.lower()
            file_name = os.path.basename(file_path)

            if ext not in self.supported_extensions:
                return {
                    "success": False,
                    "message": f"Unsupported file format: {ext}. Supported formats: {', '.join(self.supported_extensions)}",
                    "ai_analysis": f"File extension {ext} is not supported for financial data processing",
                    "agent_report": self.generate_detailed_report()
                }

            # File size validation
            file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
            if file_size_mb > self.max_file_size_mb:
                return {
                    "success": False,
                    "message": f"File too large: {file_size_mb:.2f}MB. Maximum allowed: {self.max_file_size_mb}MB",
                    "ai_analysis": f"File size {file_size_mb:.2f}MB exceeds processing limits",
                    "agent_report": self.generate_detailed_report()
                }

            # Excel file reading and basic validation
            try:
                df = pd.read_excel(file_path)
                if df.empty:
                    return {
                        "success": False,
                        "message": "File is empty or contains no data",
                        "ai_analysis": "Excel file contains no data rows",
                        "agent_report": self.generate_detailed_report()
                    }
            except Exception as e:
                return {
                    "success": False,
                    "message": f"Cannot read Excel file: {str(e)}",
                    "ai_analysis": f"Excel file reading failed: {str(e)}",
                    "agent_report": self.generate_detailed_report()
                }

            # Prepare file info for AI analysis
            file_info = {
                "name": file_name,
                "size_mb": round(file_size_mb, 2),
                "rows": len(df),
                "columns": len(df.columns),
                "extension": ext,
                "path": file_path
            }

            # AI-powered comprehensive file analysis
            ai_analysis_result = self.analyze_file_with_ai(file_path, df, file_info)

            if not ai_analysis_result["success"]:
                return {
                    "success": False,
                    "message": f"AI file analysis failed: {ai_analysis_result.get('error', 'Unknown error')}",
                    "file_info": file_info,
                    "ai_analysis": ai_analysis_result,
                    "agent_report": self.generate_detailed_report()
                }

            # Process AI analysis results
            ai_analysis = ai_analysis_result.get("structured_response")
            raw_analysis = ai_analysis_result.get("raw_response", "")

            if ai_analysis and isinstance(ai_analysis, dict):
                validation_passed = ai_analysis.get("validation_passed", False)
                quality_score = ai_analysis.get("file_quality_score", 0.0)
                readiness = ai_analysis.get("readiness_for_processing", False)
                recommendations = ai_analysis.get("recommendations", [])

                if validation_passed and readiness and quality_score >= 0.7:
                    return {
                        "success": True,
                        "message": f"AI File validation passed. Quality score: {quality_score:.2f}. File is ready for processing.",
                        "file_info": file_info,
                        "quality_score": quality_score,
                        "recommendations": recommendations,
                        "ai_analysis": ai_analysis,
                        "agent_report": self.generate_detailed_report()
                    }
                else:
                    # File has issues or low quality
                    issues = ai_analysis.get("format_analysis", {}).get("format_issues", [])
                    issues.extend(ai_analysis.get("content_analysis", {}).get("potential_issues", []))

                    return {
                        "success": False,
                        "message": f"AI File validation failed. Quality score: {quality_score:.2f}. Issues detected.",
                        "file_info": file_info,
                        "quality_score": quality_score,
                        "issues": issues,
                        "recommendations": recommendations,
                        "detailed_assessment": ai_analysis.get("detailed_assessment", ""),
                        "ai_analysis": ai_analysis,
                        "agent_report": self.generate_detailed_report()
                    }
            else:
                # Fallback if structured response parsing fails
                return {
                    "success": True,  # Basic validation passed, AI analysis available as raw text
                    "message": f"File validation completed. AI analysis available but response parsing failed.",
                    "file_info": file_info,
                    "raw_ai_analysis": raw_analysis[:500] + "..." if len(raw_analysis) > 500 else raw_analysis,
                    "ai_analysis": ai_analysis_result,
                    "agent_report": self.generate_detailed_report()
                }

        except Exception as e:
            error_msg = f"AI File validation error: {str(e)}"
            return {
                "success": False,
                "message": error_msg,
                "ai_analysis": {"error": error_msg},
                "agent_report": self.generate_detailed_report()
            }