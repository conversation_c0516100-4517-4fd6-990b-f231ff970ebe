import os
from langchain.schema import SystemMessage, HumanMessage
from core.llm_config import get_llm
 
class FileValidationAgent:
    def __init__(self):
        self.llm = get_llm()
 
    def run(self, file_path: str) -> dict:
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()
        file_name = os.path.basename(file_path)
 
        messages = [
            SystemMessage(content="You are a File Validation Agent. Check if the uploaded file is a valid Excel file (.xls or .xlsx)."),
            HumanMessage(content=f"The uploaded file is `{file_name}` with extension `{ext}`. "
                                 f"Is this a valid Excel file? Respond clearly for the frontend.")
        ]
 
        response = self.llm(messages).content
 
        is_valid = ext in [".xls"]
        return {
            "success": is_valid,
            "message": response
        }