import streamlit as st
import shutil
from utils.state import init_session_state

init_session_state()
st.title("Step 4: Upload to Final Location (Local)")

if not st.session_state.generated_file_path:
    st.warning("Please generate a file first.")
    st.stop()

dest_path = f"data/final_output/{os.path.basename(st.session_state.generated_file_path)}"
shutil.copy(st.session_state.generated_file_path, dest_path)

st.success(f"File successfully 'uploaded' to local path: {dest_path}")
st.session_state.logs.append(f"Uploaded to: {dest_path}")
