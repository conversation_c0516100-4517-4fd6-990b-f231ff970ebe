# CBRE Validation Agent - Installation Guide

## 🚀 Quick Start

### **Option 1: Automatic Installation (Recommended)**
```bash
# 1. Check and install dependencies
python check_dependencies.py

# 2. Run the application
python run_app.py
```

### **Option 2: Manual Installation**
```bash
# 1. Install all dependencies
pip install -r requirements.txt

# 2. Set up environment
cp .env.template .env
# Edit .env and add your OpenAI API key

# 3. Run the application
streamlit run frontend/main_launcher.py
```

## 📋 Dependencies

### **Required Dependencies (Basic Features)**
- `streamlit` - Web interface
- `pandas` - Data processing
- `openpyxl` - Excel file handling
- `python-dotenv` - Environment variables
- `pyyaml` - Configuration files
- `xlrd` - Excel file reading

### **Optional Dependencies (AI Features)**
- `langchain` - LLM framework
- `langchain-openai` - OpenAI integration
- `openai` - OpenAI API client

## 🔧 Installation Methods

### **Method 1: Using Dependency Checker**
```bash
python check_dependencies.py
```
This script will:
- Check which dependencies are installed
- Show detailed status report
- Offer to install missing packages automatically
- Provide manual installation commands

### **Method 2: Manual pip install**
```bash
# Install all dependencies at once
pip install streamlit pandas openpyxl python-dotenv pyyaml xlrd langchain langchain-openai openai

# Or install from requirements file
pip install -r requirements.txt
```

### **Method 3: Gradual Installation**
```bash
# Install basic dependencies first
pip install streamlit pandas openpyxl python-dotenv pyyaml xlrd

# Then install AI dependencies
pip install langchain langchain-openai openai
```

## ⚙️ Configuration

### **Environment Variables**
Create a `.env` file in the project root:
```bash
# Copy template
cp .env.template .env
```

Edit `.env` file:
```env
# OpenAI Configuration (Required for AI features)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_TEMPERATURE=0.3

# Application Configuration
APP_ENV=development
DEBUG=true
MAX_FILE_SIZE_MB=50
```

## 🚨 Troubleshooting

### **Common Issues**

#### **1. ModuleNotFoundError: No module named 'langchain'**
```bash
# Solution: Install AI dependencies
pip install langchain langchain-openai openai
```

#### **2. OpenAI API Key Missing**
```bash
# Solution: Set API key in .env file
echo "OPENAI_API_KEY=your_key_here" >> .env
```

#### **3. Import Errors**
```bash
# Solution: Check Python path and reinstall
pip uninstall -y langchain langchain-openai openai
pip install langchain langchain-openai openai
```

#### **4. Permission Errors**
```bash
# Solution: Use user installation
pip install --user -r requirements.txt
```

### **Dependency Status Check**
```bash
# Check what's installed
python -c "from core.dependency_manager import create_dependency_report; print(create_dependency_report())"

# Run integration test
python test_integration.py
```

## 🎯 Feature Availability

### **With Basic Dependencies Only**
- ✅ File upload and basic validation
- ✅ Schema structure checking
- ✅ Basic data cleaning
- ✅ File generation
- ❌ AI-powered analysis
- ❌ Intelligent recommendations

### **With Full Dependencies (AI Enabled)**
- ✅ All basic features
- ✅ AI-powered file analysis
- ✅ Intelligent schema validation
- ✅ Smart data quality assessment
- ✅ AI-driven cleaning strategies
- ✅ Comprehensive reporting
- ✅ Business context understanding

## 🔄 Fallback Behavior

The system is designed to work gracefully with missing dependencies:

1. **Missing AI Dependencies**: 
   - Uses mock AI responses
   - Shows clear warnings
   - Provides installation instructions
   - Basic functionality still works

2. **Missing Basic Dependencies**:
   - Application won't start
   - Clear error messages
   - Installation guidance provided

## 🧪 Testing Installation

### **Quick Test**
```bash
python check_dependencies.py
```

### **Comprehensive Test**
```bash
python test_integration.py
```

### **Manual Verification**
```python
# Test basic imports
python -c "import streamlit, pandas, openpyxl; print('Basic dependencies OK')"

# Test AI imports
python -c "import langchain, openai; print('AI dependencies OK')"
```

## 📞 Support

If you encounter issues:

1. **Check Dependencies**: Run `python check_dependencies.py`
2. **Check Python Version**: Requires Python 3.8+
3. **Check Virtual Environment**: Ensure you're in the correct environment
4. **Check Permissions**: Try `pip install --user` if permission denied
5. **Check Network**: Ensure internet access for package downloads

## 🎉 Success Indicators

You'll know installation is successful when:
- ✅ `python check_dependencies.py` shows all green
- ✅ `python test_integration.py` passes all tests
- ✅ `python run_app.py` starts without errors
- ✅ Web interface opens at `http://localhost:8501`

## 📝 Notes

- **Virtual Environment Recommended**: Use `venv` or `conda` for isolation
- **Python Version**: Requires Python 3.8 or higher
- **OpenAI API**: Required for AI features, get key from OpenAI website
- **Internet Required**: For initial package installation and AI API calls
