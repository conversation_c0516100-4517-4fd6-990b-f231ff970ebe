import pandas as pd
 
 
class SchemaValidationAgent:
    def __init__(self):
        self.required_columns = [
            "customer_name", "email", "phone", "car_model", "purchase_date",
            "dealer_id", "state", "price", "loan_required", "color_preference"
        ]
 
    def run(self, file_path: str) -> dict:
        try:
            df = pd.read_excel(file_path)
            actual_columns = df.columns.tolist()
 
            missing_columns = [
                col for col in self.required_columns if col not in actual_columns]
 
            if missing_columns:
                if len(missing_columns) == 0 and len(actual_columns) == len(self.required_columns):
                    return {
                        "success": False,
                        "message": "Column names mismatch, but count is same. Possible rollback scenario.",
                        "rollback": True
                    }
                return {
                    "success": False,
                    "message": f"Missing columns: {missing_columns}",
                    "rollback": False
                }
 
            return {
                "success": True,
                "message": "All required columns are present.",
                "rollback": False
            }
 
        except Exception as e:
            return {
                "success": False,
                "message": f"Schema validation error: {str(e)}",
                "rollback": False
            }