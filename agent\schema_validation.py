import pandas as pd
import json
from typing import Dict, List, Any
from core.config_loader import get_nav_schema, get_position_schema, get_metadata_config
from core.ai_agent_base import AIAgentBase

class SchemaValidationAgent(AIAgentBase):
    def __init__(self):
        super().__init__(
            agent_name="Schema Validation AI Agent",
            agent_role="Intelligent schema analysis and file type detection specialist"
        )
        self.nav_schema = get_nav_schema()
        self.position_schema = get_position_schema()
        self.metadata_config = get_metadata_config()

    def detect_file_type_with_ai(self, file_path: str, df: pd.DataFrame) -> Dict:
        """AI-powered file type detection with detailed reasoning"""

        # Prepare data for AI analysis
        file_name = file_path.lower()
        columns = list(df.columns)
        sample_data = df.head(3).to_dict('records') if not df.empty else []

        nav_schema = self.nav_schema.get("required_columns", [])
        position_schema = self.position_schema.get("required_columns", [])

        system_prompt = f"""
        You are an expert financial data analyst specializing in NAV (Net Asset Value) and Position file analysis.

        Your task is to intelligently determine if an Excel file contains NAV data or Position data based on:
        1. Filename patterns
        2. Column structure analysis
        3. Data content analysis
        4. Financial data patterns

        NAV files typically contain: {', '.join(nav_schema)}
        Position files typically contain: {', '.join(position_schema)}

        Provide your analysis in JSON format:
        {{
            "file_type": "nav|position|unknown",
            "confidence": 0.0-1.0,
            "reasoning": "detailed explanation of your analysis",
            "column_analysis": "analysis of column structure",
            "content_analysis": "analysis of data content patterns",
            "recommendations": ["list of recommendations if uncertain"]
        }}
        """

        user_prompt = f"""
        Analyze this financial Excel file:

        Filename: {file_path}
        Columns found: {columns}
        Sample data (first 3 rows): {json.dumps(sample_data, indent=2, default=str)}
        Total rows: {len(df)}

        Based on your expertise in financial data analysis, determine if this is a NAV file or Position file.
        Consider the column names, data patterns, and typical financial reporting structures.
        """

        return self.analyze_with_ai(system_prompt, user_prompt, {
            "filename": file_path,
            "columns": columns,
            "row_count": len(df),
            "sample_data": sample_data
        })

    def validate_schema_with_ai(self, df: pd.DataFrame, file_type: str) -> Dict:
        """AI-powered schema validation with detailed analysis"""

        schema = self.nav_schema if file_type == "nav" else self.position_schema
        required_columns = schema.get("required_columns", [])
        data_types = schema.get("data_types", {})
        actual_columns = list(df.columns)

        system_prompt = f"""
        You are an expert financial data schema validator specializing in {file_type.upper()} files.

        Your task is to perform intelligent schema validation by:
        1. Analyzing column presence and naming conventions
        2. Identifying potential column mapping issues
        3. Validating data types and formats
        4. Detecting rollback scenarios (same columns, different names)
        5. Providing detailed recommendations for schema issues

        Required columns for {file_type.upper()}: {required_columns}
        Expected data types: {json.dumps(data_types, indent=2)}

        Provide analysis in JSON format:
        {{
            "validation_passed": true/false,
            "file_type": "{file_type}",
            "column_analysis": {{
                "missing_columns": [],
                "extra_columns": [],
                "potential_mappings": {{}},
                "naming_issues": []
            }},
            "data_type_analysis": {{
                "correct_types": [],
                "incorrect_types": [],
                "type_recommendations": {{}}
            }},
            "rollback_scenario": {{
                "detected": true/false,
                "confidence": 0.0-1.0,
                "suggested_mappings": {{}}
            }},
            "detailed_findings": "comprehensive analysis",
            "recommendations": [],
            "severity": "low|medium|high|critical"
        }}
        """

        # Analyze sample data for type validation
        sample_data = {}
        for col in actual_columns:
            if col in df.columns:
                sample_values = df[col].dropna().head(5).tolist()
                sample_data[col] = {
                    "sample_values": sample_values,
                    "data_type": str(df[col].dtype),
                    "null_count": df[col].isnull().sum(),
                    "unique_count": df[col].nunique()
                }

        user_prompt = f"""
        Perform comprehensive schema validation for this {file_type.upper()} file:

        Actual columns found: {actual_columns}
        Required columns: {required_columns}

        Sample data analysis:
        {json.dumps(sample_data, indent=2, default=str)}

        Total rows: {len(df)}

        Analyze:
        1. Are all required columns present?
        2. Are there potential column name variations that could be mapped?
        3. Do the data types match expectations?
        4. Is this a rollback scenario where columns exist but have different names?
        5. What are the data quality issues?

        Provide detailed analysis and actionable recommendations.
        """

        return self.analyze_with_ai(system_prompt, user_prompt, {
            "file_type": file_type,
            "required_columns": required_columns,
            "actual_columns": actual_columns,
            "sample_data": sample_data,
            "row_count": len(df)
        })

    def run(self, file_path: str) -> dict:
        """Main AI-powered schema validation entry point"""
        try:
            # Load and analyze file
            df = pd.read_excel(file_path)

            if df.empty:
                return {
                    "success": False,
                    "message": "File is empty - no data to validate",
                    "ai_analysis": "File contains no data rows",
                    "agent_report": self.generate_detailed_report()
                }

            # AI-powered file type detection
            file_type_result = self.detect_file_type_with_ai(file_path, df)

            if not file_type_result["success"]:
                return {
                    "success": False,
                    "message": f"AI file type detection failed: {file_type_result.get('error', 'Unknown error')}",
                    "ai_analysis": file_type_result,
                    "agent_report": self.generate_detailed_report()
                }

            # Extract file type from AI response
            ai_response = file_type_result.get("structured_response")
            if ai_response and isinstance(ai_response, dict):
                file_type = ai_response.get("file_type", "unknown")
                confidence = ai_response.get("confidence", 0.0)
            else:
                # Fallback: try to extract from raw response
                raw_response = file_type_result.get("raw_response", "").lower()
                if "nav" in raw_response and "position" not in raw_response:
                    file_type = "nav"
                elif "position" in raw_response and "nav" not in raw_response:
                    file_type = "position"
                else:
                    file_type = "unknown"
                confidence = 0.5

            if file_type == "unknown" or confidence < 0.6:
                return {
                    "success": False,
                    "message": f"AI could not confidently determine file type. Confidence: {confidence:.2f}",
                    "rollback": False,
                    "file_type": file_type,
                    "ai_analysis": file_type_result,
                    "agent_report": self.generate_detailed_report()
                }

            # AI-powered schema validation
            schema_result = self.validate_schema_with_ai(df, file_type)

            if not schema_result["success"]:
                return {
                    "success": False,
                    "message": f"AI schema validation failed: {schema_result.get('error', 'Unknown error')}",
                    "file_type": file_type,
                    "ai_analysis": schema_result,
                    "agent_report": self.generate_detailed_report()
                }

            # Process AI validation results
            ai_validation = schema_result.get("structured_response")
            if ai_validation and isinstance(ai_validation, dict):
                validation_passed = ai_validation.get("validation_passed", False)
                rollback_info = ai_validation.get("rollback_scenario", {})

                if validation_passed:
                    return {
                        "success": True,
                        "message": f"AI Schema validation passed for {file_type.upper()} file",
                        "rollback": False,
                        "file_type": file_type,
                        "validated_columns": ai_validation.get("column_analysis", {}).get("missing_columns", []),
                        "ai_analysis": ai_validation,
                        "agent_report": self.generate_detailed_report()
                    }
                else:
                    # Check for rollback scenario
                    is_rollback = rollback_info.get("detected", False)

                    return {
                        "success": False,
                        "message": ai_validation.get("detailed_findings", "Schema validation failed"),
                        "rollback": is_rollback,
                        "file_type": file_type,
                        "expected_columns": self.nav_schema.get("required_columns", []) if file_type == "nav" else self.position_schema.get("required_columns", []),
                        "actual_columns": list(df.columns),
                        "ai_analysis": ai_validation,
                        "agent_report": self.generate_detailed_report()
                    }
            else:
                # Fallback if structured response parsing fails
                raw_analysis = schema_result.get("raw_response", "")
                return {
                    "success": False,
                    "message": f"Schema validation completed but response parsing failed. Raw AI analysis: {raw_analysis[:200]}...",
                    "rollback": False,
                    "file_type": file_type,
                    "ai_analysis": schema_result,
                    "agent_report": self.generate_detailed_report()
                }

        except Exception as e:
            error_msg = f"AI Schema validation error: {str(e)}"
            return {
                "success": False,
                "message": error_msg,
                "rollback": False,
                "file_type": "unknown",
                "ai_analysis": {"error": error_msg},
                "agent_report": self.generate_detailed_report()
            }