{"required_columns": ["UNIQUE_ID", "PORTFOLIO_ID", "NO_OF_SHARES", "NAV", "FUND_NAME"], "data_types": {"UNIQUE_ID": "string", "PORTFOLIO_ID": "string", "NO_OF_SHARES": "int", "NAV": "float", "FUND_NAME": "string"}, "cleanup_rules": {"UNIQUE_ID": {"strip_special_characters": true, "remove_total_rows": true, "extract_from_parentheses": true}, "financial_fields": ["NO_OF_SHARES", "NAV"], "numeric_cleanup": {"remove_commas": true, "convert_e_format": true, "allow_negative": false, "strip_non_numeric": true}}, "validation_rules": {"reject_if_file_empty": true, "reject_if_missing_unique_id": true, "reject_if_duplicate_unique_id": true, "reject_if_all_financial_fields_null": true, "reject_if_negative_shares": true}}