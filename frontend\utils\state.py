import streamlit as st

def init_session_state():
    if "uploaded_file" not in st.session_state:
        st.session_state.uploaded_file = None
    if "file_validation_passed" not in st.session_state:
        st.session_state.file_validation_passed = False
    if "schema_validation_passed" not in st.session_state:
        st.session_state.schema_validation_passed = False
    if "generated_file_path" not in st.session_state:
        st.session_state.generated_file_path = None
    if "logs" not in st.session_state:
        st.session_state.logs = []
