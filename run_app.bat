@echo off
echo 🏢 CBRE Validation Agent - Windows Launcher
echo ================================================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8 or higher
    pause
    exit /b 1
)

REM Run the Python startup script
python run_app.py

REM Keep window open if there's an error
if errorlevel 1 (
    echo.
    echo ❌ Application failed to start
    pause
)
