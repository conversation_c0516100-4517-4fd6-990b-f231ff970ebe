import streamlit as st
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from frontend.utils.state import init_session_state, add_log
from frontend.components.status_tracker import render_complete_sidebar
from core.master_agent import MasterAgent

# Initialize session state
init_session_state()

# Render sidebar
render_complete_sidebar()

st.title("🔍 Step 2: Validation Results")

if st.session_state.uploaded_file is None:
    st.warning("⚠️ Please upload a file first.")
    st.stop()

file_path = st.session_state.uploaded_file_path

st.markdown(f"**Validating file:** `{os.path.basename(file_path)}`")

# Initialize master agent
master_agent = MasterAgent()

# Run validation button
if st.button("🚀 Start Validation", type="primary"):
    with st.spinner("Running validation pipeline..."):

        # Handle rollback scenario if exists
        user_confirmation = None
        if st.session_state.rollback_scenario:
            user_confirmation = st.session_state.rollback_scenario

        # Run validation
        result = master_agent.run(file_path, user_confirmation)

        # Store results
        st.session_state.validation_results = result.get("results", {})

        if result["status"] == "success":
            st.success("✅ All validations passed!")
            st.session_state.file_validation_passed = True
            st.session_state.schema_validation_passed = True
            st.session_state.data_validation_passed = True
            st.session_state.detected_file_type = result.get("file_type")

            add_log(f"All validations passed for {result.get('file_type', 'unknown').upper()} file")

        elif result["status"] == "rollback":
            st.warning("⚠️ Schema Rollback Scenario Detected")
            st.session_state.rollback_scenario = result

            rollback_info = result.get("rollback_info", {})
            st.write("**Expected columns:**", rollback_info.get("expected_columns", []))
            st.write("**Actual columns:**", rollback_info.get("actual_columns", []))

            col1, col2 = st.columns(2)
            with col1:
                if st.button("✅ Proceed Anyway"):
                    st.session_state.rollback_scenario = "proceed"
                    st.rerun()
            with col2:
                if st.button("❌ Discard File"):
                    st.session_state.rollback_scenario = "discard"
                    st.rerun()

        else:
            st.error(f"❌ Validation failed at stage: {result['stage']}")
            st.error(result["message"])
            add_log(f"Validation failed: {result['message']}")

# Display detailed AI analysis results
if st.session_state.validation_results:
    st.markdown("---")
    st.subheader("🤖 AI Agent Analysis Results")

    results = st.session_state.validation_results

    # AI File Validation
    if "file_validation" in results:
        file_result = results["file_validation"]
        with st.expander("🤖 AI File Validation Agent", expanded=file_result.get("success", False)):
            if file_result.get("success"):
                st.success(file_result.get("message", "AI File validation passed"))

                # Display quality score if available
                quality_score = file_result.get("quality_score")
                if quality_score is not None:
                    st.metric("AI Quality Score", f"{quality_score:.2f}", delta=f"{(quality_score-0.5):.2f}")

                # File info
                file_info = file_result.get("file_info", {})
                if file_info:
                    col1, col2 = st.columns(2)
                    with col1:
                        st.metric("Rows", file_info.get("rows", 0))
                        st.metric("Size (MB)", file_info.get("size_mb", 0))
                    with col2:
                        st.metric("Columns", file_info.get("columns", 0))
                        st.write(f"**Extension:** {file_info.get('extension', 'Unknown')}")

                # AI Analysis
                ai_analysis = file_result.get("ai_analysis", {})
                if ai_analysis and isinstance(ai_analysis, dict):
                    st.subheader("🧠 AI Analysis")

                    # Format analysis
                    format_analysis = ai_analysis.get("format_analysis", {})
                    if format_analysis:
                        st.write("**Format Analysis:**")
                        st.write(f"- Excel Compatibility: {format_analysis.get('excel_compatibility', 'Unknown')}")
                        st.write(f"- Structure Quality: {format_analysis.get('structure_quality', 'Unknown')}")

                    # Recommendations
                    recommendations = file_result.get("recommendations", [])
                    if recommendations:
                        st.write("**AI Recommendations:**")
                        for rec in recommendations:
                            st.write(f"- {rec}")
            else:
                st.error(file_result.get("message", "AI File validation failed"))

                # Show AI analysis even for failures
                ai_analysis = file_result.get("ai_analysis", {})
                if ai_analysis:
                    st.write("**AI Analysis:**")
                    if isinstance(ai_analysis, dict):
                        st.json(ai_analysis)
                    else:
                        st.write(str(ai_analysis))

    # AI Schema Validation
    if "schema_validation" in results:
        schema_result = results["schema_validation"]
        with st.expander("🤖 AI Schema Validation Agent", expanded=schema_result.get("success", False)):
            if schema_result.get("success"):
                st.success(schema_result.get("message", "AI Schema validation passed"))
                st.write(f"**File Type Detected:** {schema_result.get('file_type', 'Unknown').upper()}")

                # AI Analysis
                ai_analysis = schema_result.get("ai_analysis", {})
                if ai_analysis and isinstance(ai_analysis, dict):
                    st.subheader("🧠 AI Schema Analysis")

                    # Column analysis
                    column_analysis = ai_analysis.get("column_analysis", {})
                    if column_analysis:
                        st.write("**Column Analysis:**")
                        missing = column_analysis.get("missing_columns", [])
                        extra = column_analysis.get("extra_columns", [])
                        if missing:
                            st.write(f"- Missing Columns: {missing}")
                        if extra:
                            st.write(f"- Extra Columns: {extra}")

                    # Data type analysis
                    data_type_analysis = ai_analysis.get("data_type_analysis", {})
                    if data_type_analysis:
                        correct_types = data_type_analysis.get("correct_types", [])
                        incorrect_types = data_type_analysis.get("incorrect_types", [])
                        if correct_types:
                            st.write(f"**Correct Data Types:** {len(correct_types)} columns")
                        if incorrect_types:
                            st.write(f"**Incorrect Data Types:** {incorrect_types}")
            else:
                st.error(schema_result.get("message", "AI Schema validation failed"))

                # Show rollback info if available
                if schema_result.get("rollback"):
                    st.warning("**Rollback Scenario Detected**")
                    st.write("Expected vs Actual columns mismatch detected by AI")

                # Show AI analysis
                ai_analysis = schema_result.get("ai_analysis", {})
                if ai_analysis:
                    st.write("**AI Analysis:**")
                    if isinstance(ai_analysis, dict):
                        st.json(ai_analysis)
                    else:
                        st.write(str(ai_analysis))

    # AI Data Validation
    if "data_validation" in results:
        data_result = results["data_validation"]
        with st.expander("🤖 AI Data Validation Agent", expanded=data_result.get("success", False)):
            if data_result.get("success"):
                st.success(data_result.get("message", "AI Data validation passed"))

                # Quality metrics
                quality_score = data_result.get("quality_score")
                if quality_score is not None:
                    st.metric("AI Data Quality Score", f"{quality_score:.2f}", delta=f"{(quality_score-0.5):.2f}")

                rows_validated = data_result.get("rows_validated", 0)
                st.metric("Rows Validated", rows_validated)

                # Warnings and recommendations
                warnings = data_result.get("warnings", [])
                if warnings:
                    st.write("**AI Warnings:**")
                    for warning in warnings:
                        st.warning(f"⚠️ {warning}")

                recommendations = data_result.get("recommendations", [])
                if recommendations:
                    st.write("**AI Recommendations:**")
                    for rec in recommendations:
                        st.write(f"- {rec}")
            else:
                st.error(data_result.get("message", "AI Data validation failed"))

                # Show detailed findings
                detailed_findings = data_result.get("detailed_findings", "")
                if detailed_findings:
                    st.write("**AI Detailed Findings:**")
                    st.write(detailed_findings)

                # Show errors and warnings
                errors = data_result.get("errors", [])
                warnings = data_result.get("warnings", [])

                if errors:
                    st.write("**Critical Issues:**")
                    for error in errors:
                        st.error(f"❌ {error}")

                if warnings:
                    st.write("**Warnings:**")
                    for warning in warnings:
                        st.warning(f"⚠️ {warning}")

    # Comprehensive AI Report
    comprehensive_report = results.get("comprehensive_report")
    if comprehensive_report:
        with st.expander("📊 Comprehensive AI Report", expanded=False):
            st.subheader("🤖 AI-Generated Comprehensive Analysis")

            if comprehensive_report.get("success"):
                report_data = comprehensive_report.get("structured_response", {})
                if report_data:
                    # Executive Summary
                    exec_summary = report_data.get("executive_summary", {})
                    if exec_summary:
                        st.write("**Executive Summary:**")
                        st.write(f"- Overall Status: {exec_summary.get('overall_status', 'Unknown')}")
                        st.write(f"- Confidence Level: {exec_summary.get('confidence_level', 0):.2f}")
                        st.write(f"- Recommendation Priority: {exec_summary.get('recommendation_priority', 'Unknown')}")

                        key_findings = exec_summary.get("key_findings", [])
                        if key_findings:
                            st.write("**Key Findings:**")
                            for finding in key_findings:
                                st.write(f"- {finding}")

                    # Risk Assessment
                    risk_assessment = report_data.get("risk_assessment", {})
                    if risk_assessment:
                        st.write("**Risk Assessment:**")
                        st.write(f"- Overall Risk: {risk_assessment.get('overall_risk_level', 'Unknown')}")
                        st.write(f"- Data Integrity Risk: {risk_assessment.get('data_integrity_risk', 'Unknown')}")
                        st.write(f"- Processing Risk: {risk_assessment.get('processing_risk', 'Unknown')}")
                else:
                    # Show raw response if structured parsing failed
                    raw_response = comprehensive_report.get("raw_response", "")
                    if raw_response:
                        st.write("**AI Report (Raw):**")
                        st.text_area("AI Analysis", raw_response, height=200)
            else:
                st.error("Comprehensive AI report generation failed")
                error = comprehensive_report.get("error", "Unknown error")
                st.write(f"Error: {error}")

    # Agent Reports
    agent_reports = results.get("agent_reports", [])
    if agent_reports:
        with st.expander("🤖 Individual AI Agent Reports", expanded=False):
            for i, report in enumerate(agent_reports):
                if report:
                    st.subheader(f"Agent {i+1}: {report.get('agent_name', 'Unknown Agent')}")
                    st.write(f"**Role:** {report.get('agent_role', 'Unknown')}")
                    st.write(f"**Interactions:** {report.get('total_interactions', 0)}")

                    analysis_summary = report.get("analysis_summary", "")
                    if analysis_summary:
                        st.write("**Analysis Summary:**")
                        st.write(analysis_summary)

                    recommendations = report.get("recommendations", [])
                    if recommendations:
                        st.write("**Recommendations:**")
                        for rec in recommendations:
                            st.write(f"- {rec}")

                    st.markdown("---")

# Navigation
if st.session_state.file_validation_passed and st.session_state.schema_validation_passed and st.session_state.data_validation_passed:
    st.markdown("---")
    st.success("🎉 Ready for file generation!")
    st.markdown("Click on **'3. File Creation'** in the sidebar to proceed.")
