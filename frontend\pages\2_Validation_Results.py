import streamlit as st
from utils.state import init_session_state
from agents import file_validation, schema_validation

init_session_state()
st.title("Step 2: File + Schema Validation")

if st.session_state.uploaded_file is None:
    st.warning("Please upload a file first.")
    st.stop()

# File validation
file_valid, file_errors = file_validation.validate(st.session_state.uploaded_file)

if file_valid:
    st.success("File structure is valid.")
    st.session_state.file_validation_passed = True
    st.session_state.logs.append("File validation passed.")
else:
    st.error("File structure issues found:")
    for err in file_errors:
        st.write(f"- {err}")
    st.stop()

# Schema validation
schema_valid, schema_errors = schema_validation.validate(st.session_state.uploaded_file)

if schema_valid:
    st.success("Schema validation passed.")
    st.session_state.schema_validation_passed = True
    st.session_state.logs.append("Schema validation passed.")
else:
    st.error("Schema validation failed:")
    for err in schema_errors:
        st.write(f"- {err}")
