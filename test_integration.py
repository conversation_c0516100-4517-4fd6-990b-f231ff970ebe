#!/usr/bin/env python3
"""
Integration test script for CBRE Validation Agent
Tests all components and their integration
"""

import os
import sys
import pandas as pd
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_config_loading():
    """Test configuration loading"""
    print("🔧 Testing configuration loading...")
    try:
        from core.config_loader import (
            get_paths_config, get_metadata_config, 
            get_nav_schema, get_position_schema,
            ensure_directories_exist
        )
        
        paths_config = get_paths_config()
        metadata_config = get_metadata_config()
        nav_schema = get_nav_schema()
        position_schema = get_position_schema()
        
        print(f"✅ Paths config loaded: {len(paths_config)} keys")
        print(f"✅ Metadata config loaded: {len(metadata_config)} keys")
        print(f"✅ NAV schema loaded: {len(nav_schema)} keys")
        print(f"✅ Position schema loaded: {len(position_schema)} keys")
        
        ensure_directories_exist()
        print("✅ Directories created successfully")
        
        return True
    except Exception as e:
        print(f"❌ Config loading failed: {e}")
        return False

def test_ai_agent_classes():
    """Test all AI agent classes can be imported and initialized"""
    print("\n🤖 Testing AI agent classes...")
    try:
        from agent.file_validation import FileValidationAgent
        from agent.schema_validation import SchemaValidationAgent
        from agent.data_validation import DataValidationAgent
        from agent.file_upload import FileUploadAgent
        from agent.file_creation import FileCreationAgent
        from core.ai_agent_base import AIAgentBase
        from core.ai_reporting import AIReportGenerator

        # Test AI Agent Base
        try:
            base_agent = AIAgentBase("Test Agent", "Test Role")
            print("✅ AIAgentBase initialized")
        except Exception as e:
            print(f"⚠️ AIAgentBase init warning: {e}")

        # Test AI agents initialization
        try:
            file_agent = FileValidationAgent()
            print("✅ AI FileValidationAgent initialized")
            print(f"   - Agent Name: {file_agent.agent_name}")
            print(f"   - Agent Role: {file_agent.agent_role}")
        except Exception as e:
            print(f"⚠️ AI FileValidationAgent init warning: {e}")

        try:
            schema_agent = SchemaValidationAgent()
            print("✅ AI SchemaValidationAgent initialized")
            print(f"   - Agent Name: {schema_agent.agent_name}")
            print(f"   - Agent Role: {schema_agent.agent_role}")
        except Exception as e:
            print(f"⚠️ AI SchemaValidationAgent init warning: {e}")

        try:
            data_agent = DataValidationAgent()
            print("✅ AI DataValidationAgent initialized")
            print(f"   - Agent Name: {data_agent.agent_name}")
            print(f"   - Agent Role: {data_agent.agent_role}")
        except Exception as e:
            print(f"⚠️ AI DataValidationAgent init warning: {e}")

        upload_agent = FileUploadAgent()
        print("✅ FileUploadAgent initialized (traditional)")

        creation_agent = FileCreationAgent()
        print("✅ FileCreationAgent initialized (traditional)")

        try:
            report_generator = AIReportGenerator()
            print("✅ AI ReportGenerator initialized")
            print(f"   - Agent Name: {report_generator.agent_name}")
            print(f"   - Agent Role: {report_generator.agent_role}")
        except Exception as e:
            print(f"⚠️ AI ReportGenerator init warning: {e}")

        return True
    except Exception as e:
        print(f"❌ AI Agent class test failed: {e}")
        return False

def test_ai_master_agent():
    """Test AI master agent initialization"""
    print("\n🎯 Testing AI Master Agent...")
    try:
        from core.master_agent import MasterAgent

        master = MasterAgent()
        print("✅ AI MasterAgent initialized successfully")
        print(f"   - Agent Name: {master.agent_name}")
        print(f"   - Agent Role: {master.agent_role}")
        print(f"   - Has File Validator: {hasattr(master, 'file_validator')}")
        print(f"   - Has Schema Validator: {hasattr(master, 'schema_validator')}")
        print(f"   - Has Data Validator: {hasattr(master, 'data_validator')}")
        print(f"   - Has Report Generator: {hasattr(master, 'report_generator')}")
        return True
    except Exception as e:
        print(f"❌ AI MasterAgent test failed: {e}")
        return False

def create_test_files():
    """Create test Excel files for validation"""
    print("\n📄 Creating test files...")
    try:
        os.makedirs("data/test", exist_ok=True)
        
        # Create NAV test file
        nav_data = {
            "UNIQUE_ID": ["NAV001", "NAV002", "NAV003"],
            "PORTFOLIO_ID": ["PORT001", "PORT002", "PORT003"],
            "REGISTERED_HOLDER": ["Holder A", "Holder B", "Holder C"],
            "NAV": [1000.50, 2000.75, 3000.25],
            "OWNERSHIP_PERCENTAGE": [10.5, 20.3, 15.7],
            "CAPITAL_CALLED": [500.0, 1000.0, 750.0],
            "NO_OF_SHARES": [100, 200, 150],
            "COMMITTED_CAPITAL": [5000.0, 10000.0, 7500.0],
            "PERIOD": ["2024-01", "2024-01", "2024-01"],
            "FUND_NAME": ["Fund A", "Fund B", "Fund C"]
        }
        
        nav_df = pd.DataFrame(nav_data)
        nav_file = "data/test/test_nav_file.xlsx"
        nav_df.to_excel(nav_file, index=False)
        print(f"✅ NAV test file created: {nav_file}")
        
        # Create Position test file
        position_data = {
            "UNIQUE_ID": ["POS001", "POS002", "POS003"],
            "PORTFOLIO_ID": ["PORT001", "PORT002", "PORT003"],
            "NO_OF_SHARES": [100, 200, 150],
            "NAV": [1000.50, 2000.75, 3000.25],
            "FUND_NAME": ["Fund A", "Fund B", "Fund C"]
        }
        
        position_df = pd.DataFrame(position_data)
        position_file = "data/test/test_position_file.xlsx"
        position_df.to_excel(position_file, index=False)
        print(f"✅ Position test file created: {position_file}")
        
        return nav_file, position_file
    except Exception as e:
        print(f"❌ Test file creation failed: {e}")
        return None, None

def test_ai_validation_pipeline(test_file, file_type):
    """Test the AI validation pipeline (basic functionality without full LLM)"""
    print(f"\n🔍 Testing AI validation pipeline for {file_type.upper()} file...")
    try:
        from agent.schema_validation import SchemaValidationAgent
        from agent.data_validation import DataValidationAgent
        from agent.file_creation import FileCreationAgent
        from agent.file_validation import FileValidationAgent

        # Test AI file validation
        print("   Testing AI File Validation...")
        file_agent = FileValidationAgent()
        # Test basic functionality (will skip LLM if not available)
        print(f"   - AI File Agent ready: {hasattr(file_agent, 'analyze_file_with_ai')}")

        # Test AI schema validation
        print("   Testing AI Schema Validation...")
        schema_agent = SchemaValidationAgent()
        print(f"   - AI Schema Agent ready: {hasattr(schema_agent, 'detect_file_type_with_ai')}")
        print(f"   - AI Schema Agent ready: {hasattr(schema_agent, 'validate_schema_with_ai')}")

        # Test AI data validation
        print("   Testing AI Data Validation...")
        data_agent = DataValidationAgent()
        print(f"   - AI Data Agent ready: {hasattr(data_agent, 'analyze_data_quality_with_ai')}")

        # Test file creation (traditional)
        creation_agent = FileCreationAgent()
        creation_result = creation_agent.run(test_file, file_type)
        print(f"✅ File creation: {creation_result['success']}")
        if creation_result['success']:
            print(f"   Output: {creation_result['output_path']}")

        print("✅ AI validation pipeline components ready")
        return True
    except Exception as e:
        print(f"❌ AI validation pipeline test failed: {e}")
        return False

def test_ai_reporting():
    """Test AI reporting system"""
    print("\n📊 Testing AI Reporting System...")
    try:
        from core.ai_reporting import AIReportGenerator

        report_gen = AIReportGenerator()
        print("✅ AI Report Generator initialized")
        print(f"   - Agent Name: {report_gen.agent_name}")
        print(f"   - Agent Role: {report_gen.agent_role}")
        print(f"   - Has comprehensive report method: {hasattr(report_gen, 'generate_comprehensive_report')}")
        print(f"   - Has performance report method: {hasattr(report_gen, 'generate_agent_performance_report')}")
        print(f"   - Has dashboard data method: {hasattr(report_gen, 'create_executive_dashboard_data')}")

        return True
    except Exception as e:
        print(f"❌ AI Reporting test failed: {e}")
        return False

def test_frontend_imports():
    """Test frontend component imports"""
    print("\n🖥️ Testing frontend imports...")
    try:
        # Test if we can import frontend components
        sys.path.append(os.path.join(os.path.dirname(__file__), 'frontend'))
        
        from frontend.utils.state import init_session_state, add_log
        from frontend.components.status_tracker import render_status_tracker
        
        print("✅ Frontend utils imported successfully")
        print("✅ Frontend components imported successfully")
        
        return True
    except Exception as e:
        print(f"❌ Frontend import test failed: {e}")
        return False

def main():
    """Run all integration tests"""
    print("🚀 CBRE Validation Agent - Integration Test")
    print("=" * 50)
    
    test_results = []
    
    # Run tests
    test_results.append(("Config Loading", test_config_loading()))
    test_results.append(("AI Agent Classes", test_ai_agent_classes()))
    test_results.append(("AI Master Agent", test_ai_master_agent()))
    test_results.append(("AI Reporting System", test_ai_reporting()))
    test_results.append(("Frontend Imports", test_frontend_imports()))

    # Create and test with sample files
    nav_file, position_file = create_test_files()
    if nav_file and position_file:
        test_results.append(("AI NAV Pipeline", test_ai_validation_pipeline(nav_file, "nav")))
        test_results.append(("AI Position Pipeline", test_ai_validation_pipeline(position_file, "position")))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The system is ready to use.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
