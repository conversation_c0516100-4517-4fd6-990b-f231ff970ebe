import pandas as pd
import os
import json
from datetime import datetime
from typing import Dict, List, Any
from core.config_loader import get_paths_config
from core.ai_agent_base import AIAgentBase

class FileCreationAgent(AIAgentBase):
    def __init__(self):
        super().__init__(
            agent_name="File Creation AI Agent",
            agent_role="Intelligent data cleaning and file generation specialist"
        )
        self.paths_config = get_paths_config()
        self.generated_dir = self.paths_config.get("generated_dir", "data/generated")

    def analyze_data_cleaning_with_ai(self, df: pd.DataFrame, file_type: str, validation_results: Dict = None) -> Dict:
        """AI-powered data cleaning analysis and strategy"""

        # Prepare comprehensive data analysis for AI
        data_profile = self._create_cleaning_data_profile(df)
        validation_context = validation_results or {}

        # Get advanced schema rules
        from core.config_loader import get_nav_schema, get_position_schema
        schema = get_nav_schema() if file_type == "nav" else get_position_schema()
        cleanup_rules = schema.get("cleanup_rules", {})
        validation_rules = schema.get("validation_rules", {})

        system_prompt = f"""
        You are an expert financial data cleaning specialist with deep knowledge of {file_type.upper()} file processing.

        Your expertise includes:
        1. Financial data quality assessment and cleaning
        2. Business rule-based data filtering
        3. Data integrity preservation during cleaning
        4. Portfolio management data standards
        5. Regulatory compliance requirements

        Analyze the data and provide intelligent cleaning strategy in JSON format:
        {{
            "cleaning_strategy": {{
                "approach": "conservative|standard|aggressive",
                "priority": "data_integrity|completeness|compliance",
                "risk_tolerance": "low|medium|high"
            }},
            "data_quality_assessment": {{
                "overall_quality": 0.0-1.0,
                "completeness_score": 0.0-1.0,
                "consistency_score": 0.0-1.0,
                "integrity_score": 0.0-1.0
            }},
            "cleaning_recommendations": {{
                "rows_to_remove": {{
                    "invalid_unique_ids": [],
                    "total_summary_rows": [],
                    "all_zero_financial_rows": [],
                    "duplicate_entries": [],
                    "incomplete_records": [],
                    "outlier_records": []
                }},
                "data_transformations": {{
                    "standardize_formats": [],
                    "correct_data_types": [],
                    "normalize_values": []
                }},
                "quality_improvements": {{
                    "fill_missing_values": {{}},
                    "correct_inconsistencies": [],
                    "validate_ranges": {{}}
                }}
            }},
            "cleaning_impact": {{
                "estimated_rows_removed": 0,
                "estimated_rows_modified": 0,
                "data_loss_percentage": 0.0,
                "quality_improvement_score": 0.0-1.0
            }},
            "business_justification": {{
                "cleaning_rationale": "detailed explanation",
                "compliance_requirements": [],
                "risk_mitigation": [],
                "data_governance_alignment": []
            }},
            "post_cleaning_validation": {{
                "required_checks": [],
                "quality_thresholds": {{}},
                "acceptance_criteria": []
            }}
        }}
        """

        user_prompt = f"""
        Analyze this {file_type.upper()} file and provide intelligent data cleaning strategy:

        Data Profile:
        {json.dumps(data_profile, indent=2, default=str)}

        Validation Context:
        {json.dumps(validation_context, indent=2, default=str)}

        Advanced Schema Cleanup Rules:
        {json.dumps(cleanup_rules, indent=2)}

        Schema Validation Rules:
        {json.dumps(validation_rules, indent=2)}

        Enhanced Analysis Requirements:
        1. Apply schema-defined cleanup rules for UNIQUE_ID and financial fields
        2. Implement numeric cleanup (remove commas, convert e-format, handle negatives)
        3. Follow validation rules (reject criteria) from schema
        4. Identify rows that should be removed based on business rules
        5. Detect data quality issues that need correction
        6. Assess impact of cleaning on data integrity
        7. Provide business justification for cleaning decisions
        8. Recommend data transformations based on schema cleanup rules
        9. Ensure compliance with financial data standards

        Schema-Specific Focus:
        - Apply UNIQUE_ID cleanup: strip special chars, remove TOTAL rows, extract from parentheses
        - Apply numeric cleanup to financial fields: remove commas, convert formats
        - Validate against rejection criteria: empty file, missing/duplicate UNIQUE_ID, all-null financials
        - Preserve data integrity while improving quality
        - Business-justified cleaning decisions with schema compliance
        - Minimal data loss with maximum quality improvement
        """

        return self.analyze_with_ai(system_prompt, user_prompt, {
            "file_type": file_type,
            "data_profile": data_profile,
            "validation_context": validation_context
        })

    def _create_cleaning_data_profile(self, df: pd.DataFrame) -> Dict:
        """Create comprehensive data profile for cleaning analysis"""
        profile = {
            "basic_info": {
                "total_rows": len(df),
                "total_columns": len(df.columns),
                "columns": list(df.columns)
            },
            "data_quality_issues": {},
            "potential_cleaning_targets": {},
            "sample_data": {}
        }

        # Analyze potential cleaning targets
        if "UNIQUE_ID" in df.columns:
            uid_analysis = {
                "null_count": df["UNIQUE_ID"].isnull().sum(),
                "blank_count": (df["UNIQUE_ID"].astype(str).str.strip() == "").sum(),
                "duplicate_count": df["UNIQUE_ID"].duplicated().sum(),
                "total_prefix_count": df["UNIQUE_ID"].astype(str).str.upper().str.startswith("TOTAL").sum(),
                "sample_duplicates": df[df["UNIQUE_ID"].duplicated()]["UNIQUE_ID"].head(5).tolist(),
                "sample_total_prefixed": df[df["UNIQUE_ID"].astype(str).str.upper().str.startswith("TOTAL")]["UNIQUE_ID"].head(5).tolist()
            }
            profile["potential_cleaning_targets"]["unique_id_issues"] = uid_analysis

        # Analyze financial fields
        financial_fields = ["NAV", "OWNERSHIP_PERCENTAGE", "CAPITAL_CALLED", "NO_OF_SHARES", "COMMITTED_CAPITAL"]
        available_financial_fields = [col for col in financial_fields if col in df.columns]

        if available_financial_fields:
            # Check for all-zero rows
            all_zero_mask = (df[available_financial_fields].fillna(0) == 0).all(axis=1)
            all_zero_count = all_zero_mask.sum()

            profile["potential_cleaning_targets"]["all_zero_financial_rows"] = {
                "count": int(all_zero_count),
                "percentage": float((all_zero_count / len(df)) * 100),
                "sample_indices": df[all_zero_mask].index[:5].tolist()
            }

            # Analyze each financial field
            for field in available_financial_fields:
                field_analysis = {
                    "null_count": df[field].isnull().sum(),
                    "zero_count": (df[field] == 0).sum(),
                    "negative_count": (df[field] < 0).sum() if df[field].dtype in ['int64', 'float64'] else 0,
                    "mean": df[field].mean() if df[field].dtype in ['int64', 'float64'] else None,
                    "std": df[field].std() if df[field].dtype in ['int64', 'float64'] else None
                }
                profile["data_quality_issues"][field] = field_analysis

        # Sample data for AI analysis
        profile["sample_data"] = {
            "first_5_rows": df.head(5).to_dict('records'),
            "last_5_rows": df.tail(5).to_dict('records')
        }

        return profile

    def apply_ai_cleaning_strategy(self, df: pd.DataFrame, cleaning_analysis: Dict) -> pd.DataFrame:
        """Apply AI-recommended cleaning strategy to the data"""

        if not cleaning_analysis.get("success"):
            # Fallback to basic cleaning if AI analysis failed
            return self._apply_basic_cleaning(df)

        ai_recommendations = cleaning_analysis.get("structured_response", {})
        if not ai_recommendations:
            return self._apply_basic_cleaning(df)

        cleaned_df = df.copy()
        cleaning_recs = ai_recommendations.get("cleaning_recommendations", {})
        rows_to_remove = cleaning_recs.get("rows_to_remove", {})

        # Apply AI-recommended row removals
        try:
            # Remove invalid UNIQUE_IDs
            invalid_uids = rows_to_remove.get("invalid_unique_ids", [])
            if invalid_uids and "UNIQUE_ID" in cleaned_df.columns:
                mask = ~cleaned_df["UNIQUE_ID"].isin(invalid_uids)
                cleaned_df = cleaned_df[mask]

            # Remove TOTAL summary rows
            if "UNIQUE_ID" in cleaned_df.columns:
                mask_total = ~cleaned_df["UNIQUE_ID"].astype(str).str.upper().str.startswith("TOTAL")
                cleaned_df = cleaned_df[mask_total]

            # Remove all-zero financial rows if recommended
            financial_fields = ["NAV", "OWNERSHIP_PERCENTAGE", "CAPITAL_CALLED", "NO_OF_SHARES", "COMMITTED_CAPITAL"]
            available_fields = [col for col in financial_fields if col in cleaned_df.columns]

            if available_fields:
                mask_not_all_zero = ~(cleaned_df[available_fields].fillna(0) == 0).all(axis=1)
                cleaned_df = cleaned_df[mask_not_all_zero]

            # Remove null/blank UNIQUE_IDs
            if "UNIQUE_ID" in cleaned_df.columns:
                mask_valid_uid = ~(cleaned_df["UNIQUE_ID"].isnull() | (cleaned_df["UNIQUE_ID"].astype(str).str.strip() == ""))
                cleaned_df = cleaned_df[mask_valid_uid]

            # Remove duplicates
            if "UNIQUE_ID" in cleaned_df.columns:
                cleaned_df = cleaned_df.drop_duplicates(subset=["UNIQUE_ID"], keep="first")

        except Exception as e:
            self.log_interaction("AI Cleaning Application Failed", f"Error applying AI recommendations: {str(e)}")
            # Fallback to basic cleaning
            return self._apply_basic_cleaning(df)

        return cleaned_df.reset_index(drop=True)

    def _apply_basic_cleaning(self, df: pd.DataFrame) -> pd.DataFrame:
        """Fallback basic cleaning method"""
        cleaned_df = df.copy()

        # Basic cleaning rules
        if "UNIQUE_ID" in cleaned_df.columns:
            mask_total = ~cleaned_df["UNIQUE_ID"].astype(str).str.upper().str.startswith("TOTAL")
            cleaned_df = cleaned_df[mask_total]

            mask_valid_uid = ~(cleaned_df["UNIQUE_ID"].isnull() | (cleaned_df["UNIQUE_ID"].astype(str).str.strip() == ""))
            cleaned_df = cleaned_df[mask_valid_uid]

            cleaned_df = cleaned_df.drop_duplicates(subset=["UNIQUE_ID"], keep="first")

        return cleaned_df.reset_index(drop=True)

    def generate_with_ai_analysis(self, validated_file_path: str, file_type: str = "nav", validation_results: Dict = None) -> Dict:
        """AI-powered file generation with intelligent cleaning"""
        try:
            os.makedirs(self.generated_dir, exist_ok=True)

            # Read the validated file
            df = pd.read_excel(validated_file_path)
            original_row_count = len(df)

            # AI-powered cleaning analysis
            cleaning_analysis = self.analyze_data_cleaning_with_ai(df, file_type, validation_results)

            # Apply AI-recommended cleaning
            cleaned_df = self.apply_ai_cleaning_strategy(df, cleaning_analysis)
            cleaned_row_count = len(cleaned_df)

            # Generate output filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{file_type.upper()}_AI_Cleaned_{timestamp}.xlsx"
            output_path = os.path.join(self.generated_dir, filename)

            # Save cleaned data
            cleaned_df.to_excel(output_path, index=False)

            # Calculate cleaning impact
            rows_removed = original_row_count - cleaned_row_count
            removal_percentage = (rows_removed / original_row_count) * 100 if original_row_count > 0 else 0

            return {
                "success": True,
                "output_path": output_path,
                "cleaning_analysis": cleaning_analysis,
                "cleaning_impact": {
                    "original_rows": original_row_count,
                    "cleaned_rows": cleaned_row_count,
                    "rows_removed": rows_removed,
                    "removal_percentage": round(removal_percentage, 2)
                }
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"AI file generation error: {str(e)}"
            }

    def generate(self, validated_file_path: str, file_type: str = "nav") -> str:
        """Generate cleaned output file (backward compatibility)"""
        result = self.generate_with_ai_analysis(validated_file_path, file_type)
        if result["success"]:
            return result["output_path"]
        else:
            raise Exception(result["error"])

    def preview(self, file_path: str, rows: int = 20) -> pd.DataFrame:
        """Preview the generated file"""
        try:
            df = pd.read_excel(file_path)
            return df.head(rows)
        except Exception as e:
            raise Exception(f"Preview error: {str(e)}")

    def get_file_stats(self, file_path: str) -> dict:
        """Get statistics about the generated file"""
        try:
            df = pd.read_excel(file_path)
            return {
                "total_rows": len(df),
                "total_columns": len(df.columns),
                "columns": df.columns.tolist(),
                "file_size_mb": round(os.path.getsize(file_path) / (1024 * 1024), 2)
            }
        except Exception as e:
            return {"error": f"Stats error: {str(e)}"}

    def run(self, validated_file_path: str, file_type: str = "nav", validation_results: Dict = None) -> dict:
        """Main AI-powered file creation entry point"""
        try:
            # AI-powered file generation
            generation_result = self.generate_with_ai_analysis(validated_file_path, file_type, validation_results)

            if not generation_result["success"]:
                return {
                    "success": False,
                    "message": f"AI file generation failed: {generation_result.get('error', 'Unknown error')}",
                    "file_type": file_type,
                    "ai_analysis": generation_result,
                    "agent_report": self.generate_detailed_report()
                }

            output_path = generation_result["output_path"]
            cleaning_analysis = generation_result["cleaning_analysis"]
            cleaning_impact = generation_result["cleaning_impact"]

            # Get file statistics
            stats = self.get_file_stats(output_path)

            # Process AI cleaning analysis
            ai_analysis = cleaning_analysis.get("structured_response", {})
            raw_analysis = cleaning_analysis.get("raw_response", "")

            if ai_analysis and isinstance(ai_analysis, dict):
                cleaning_strategy = ai_analysis.get("cleaning_strategy", {})
                quality_assessment = ai_analysis.get("data_quality_assessment", {})
                business_justification = ai_analysis.get("business_justification", {})

                return {
                    "success": True,
                    "message": f"AI-powered file generation completed successfully. {cleaning_impact['rows_removed']} rows removed ({cleaning_impact['removal_percentage']:.1f}%)",
                    "output_path": output_path,
                    "file_type": file_type,
                    "stats": stats,
                    "cleaning_impact": cleaning_impact,
                    "ai_analysis": {
                        "cleaning_strategy": cleaning_strategy,
                        "quality_assessment": quality_assessment,
                        "business_justification": business_justification,
                        "cleaning_recommendations": ai_analysis.get("cleaning_recommendations", {}),
                        "post_cleaning_validation": ai_analysis.get("post_cleaning_validation", {})
                    },
                    "agent_report": self.generate_detailed_report()
                }
            else:
                # Fallback if structured response parsing fails
                return {
                    "success": True,
                    "message": f"File generation completed. {cleaning_impact['rows_removed']} rows removed ({cleaning_impact['removal_percentage']:.1f}%). AI analysis available as raw text.",
                    "output_path": output_path,
                    "file_type": file_type,
                    "stats": stats,
                    "cleaning_impact": cleaning_impact,
                    "raw_ai_analysis": raw_analysis[:500] + "..." if len(raw_analysis) > 500 else raw_analysis,
                    "ai_analysis": cleaning_analysis,
                    "agent_report": self.generate_detailed_report()
                }

        except Exception as e:
            error_msg = f"AI File creation error: {str(e)}"
            return {
                "success": False,
                "message": error_msg,
                "file_type": file_type,
                "ai_analysis": {"error": error_msg},
                "agent_report": self.generate_detailed_report()
            }

# Backward compatibility functions
def generate(validated_file, output_dir="data/generated"):
    """Backward compatibility function"""
    agent = FileCreationAgent()
    return agent.generate(validated_file)

def preview(file_path):
    """Backward compatibility function"""
    agent = FileCreationAgent()
    return agent.preview(file_path)
