import streamlit as st
from utils.state import init_session_state
import os

init_session_state()
st.title("Step 1: Upload Excel File")

uploaded_file = st.file_uploader("Upload your NAV or Position Excel file", type=["xlsx"])

if uploaded_file:
    st.session_state.uploaded_file = uploaded_file
    file_path = os.path.join("data/uploads", uploaded_file.name)
    with open(file_path, "wb") as f:
        f.write(uploaded_file.read())
    st.success(f"File uploaded to {file_path}")
    st.session_state.logs.append(f"File uploaded: {uploaded_file.name}")
