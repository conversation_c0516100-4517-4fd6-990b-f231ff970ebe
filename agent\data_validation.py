import pandas as pd
import json
from typing import Dict, List, Any
from core.config_loader import get_nav_schema, get_position_schema
from core.ai_agent_base import AIAgentBase

class DataValidationAgent(AIAgentBase):
    def __init__(self):
        super().__init__(
            agent_name="Data Validation AI Agent",
            agent_role="Intelligent financial data quality analyst and business rule validator"
        )
        self.nav_schema = get_nav_schema()
        self.position_schema = get_position_schema()

    def analyze_data_quality_with_ai(self, df: pd.DataFrame, file_type: str) -> Dict:
        """AI-powered comprehensive data quality analysis"""

        # Prepare comprehensive data analysis
        data_profile = self._create_data_profile(df)
        business_rules = self._get_business_rules(file_type)

        system_prompt = f"""
        You are an expert financial data quality analyst specializing in {file_type.upper()} file validation.

        Your expertise includes:
        1. Financial data integrity validation
        2. Business rule compliance checking
        3. Data anomaly detection
        4. Portfolio management data standards
        5. Regulatory compliance requirements

        Business Rules for {file_type.upper()} files:
        {json.dumps(business_rules, indent=2)}

        Analyze the data and provide comprehensive validation results in JSON format:
        {{
            "overall_quality_score": 0.0-1.0,
            "validation_passed": true/false,
            "critical_issues": [],
            "warnings": [],
            "data_quality_analysis": {{
                "completeness": {{
                    "score": 0.0-1.0,
                    "missing_data_issues": [],
                    "null_value_analysis": {{}}
                }},
                "consistency": {{
                    "score": 0.0-1.0,
                    "duplicate_issues": [],
                    "format_inconsistencies": []
                }},
                "validity": {{
                    "score": 0.0-1.0,
                    "invalid_values": [],
                    "range_violations": [],
                    "type_mismatches": []
                }},
                "business_rules": {{
                    "score": 0.0-1.0,
                    "rule_violations": [],
                    "compliance_issues": []
                }}
            }},
            "anomaly_detection": {{
                "statistical_outliers": [],
                "pattern_anomalies": [],
                "suspicious_entries": []
            }},
            "recommendations": [],
            "detailed_findings": "comprehensive analysis explanation",
            "rows_to_exclude": [],
            "data_cleaning_suggestions": []
        }}
        """

        user_prompt = f"""
        Perform comprehensive data quality analysis for this {file_type.upper()} file:

        Data Profile:
        {json.dumps(data_profile, indent=2, default=str)}

        Key Analysis Points:
        1. Validate UNIQUE_ID integrity (no nulls, no duplicates, no 'TOTAL' prefixes)
        2. Check financial data consistency and reasonableness
        3. Identify rows with all-zero financial values
        4. Validate data types and formats
        5. Detect statistical outliers and anomalies
        6. Check business rule compliance
        7. Assess overall data quality

        Provide detailed analysis with specific row numbers and values where issues are found.
        Focus on actionable insights and clear recommendations for data improvement.
        """

        return self.analyze_with_ai(system_prompt, user_prompt, {
            "file_type": file_type,
            "data_profile": data_profile,
            "business_rules": business_rules
        })

    def _create_data_profile(self, df: pd.DataFrame) -> Dict:
        """Create comprehensive data profile for AI analysis"""
        profile = {
            "basic_info": {
                "total_rows": len(df),
                "total_columns": len(df.columns),
                "columns": list(df.columns)
            },
            "data_quality_metrics": {},
            "sample_data": {},
            "statistical_summary": {}
        }

        # Analyze each column
        for col in df.columns:
            col_data = df[col]

            profile["data_quality_metrics"][col] = {
                "null_count": col_data.isnull().sum(),
                "null_percentage": (col_data.isnull().sum() / len(df)) * 100,
                "unique_count": col_data.nunique(),
                "data_type": str(col_data.dtype),
                "memory_usage": col_data.memory_usage(deep=True)
            }

            # Sample values (first 5 non-null)
            sample_values = col_data.dropna().head(5).tolist()
            profile["sample_data"][col] = sample_values

            # Statistical summary for numeric columns
            if pd.api.types.is_numeric_dtype(col_data):
                profile["statistical_summary"][col] = {
                    "mean": col_data.mean() if not col_data.empty else None,
                    "median": col_data.median() if not col_data.empty else None,
                    "std": col_data.std() if not col_data.empty else None,
                    "min": col_data.min() if not col_data.empty else None,
                    "max": col_data.max() if not col_data.empty else None,
                    "zero_count": (col_data == 0).sum(),
                    "negative_count": (col_data < 0).sum() if col_data.dtype in ['int64', 'float64'] else 0
                }

        # Special analysis for UNIQUE_ID if present
        if "UNIQUE_ID" in df.columns:
            uid_col = df["UNIQUE_ID"].astype(str)
            profile["unique_id_analysis"] = {
                "total_count": len(uid_col),
                "unique_count": uid_col.nunique(),
                "duplicate_count": len(uid_col) - uid_col.nunique(),
                "blank_count": (uid_col.str.strip() == "").sum(),
                "total_prefix_count": uid_col.str.upper().str.startswith("TOTAL").sum(),
                "sample_duplicates": uid_col[uid_col.duplicated()].head(5).tolist(),
                "sample_total_prefixed": uid_col[uid_col.str.upper().str.startswith("TOTAL")].head(5).tolist()
            }

        return profile

    def _get_business_rules(self, file_type: str) -> Dict:
        """Get business rules for the specific file type"""
        common_rules = {
            "unique_id_rules": {
                "required": True,
                "no_nulls": True,
                "no_duplicates": True,
                "no_total_prefix": True,
                "description": "UNIQUE_ID must be present, unique, non-null, and not start with 'TOTAL'"
            },
            "financial_data_rules": {
                "no_all_zero_rows": True,
                "reasonable_ranges": True,
                "description": "Financial data should not have all-zero rows and should be within reasonable ranges"
            }
        }

        if file_type == "nav":
            nav_rules = {
                "nav_specific_rules": {
                    "nav_positive": "NAV values should typically be positive",
                    "ownership_percentage_range": "Ownership percentage should be between 0-100",
                    "capital_consistency": "Capital called should not exceed committed capital"
                }
            }
            common_rules.update(nav_rules)

        elif file_type == "position":
            position_rules = {
                "position_specific_rules": {
                    "shares_positive": "Number of shares should be positive",
                    "nav_consistency": "NAV should be consistent with share count"
                }
            }
            common_rules.update(position_rules)

        return common_rules

    def run(self, file_path: str, file_type: str = "nav") -> dict:
        """Main AI-powered data validation entry point"""
        try:
            # Load and validate file
            df = pd.read_excel(file_path)

            if df.empty:
                return {
                    "success": False,
                    "message": "File is empty - no data to validate",
                    "file_type": file_type,
                    "ai_analysis": "File contains no data rows",
                    "agent_report": self.generate_detailed_report()
                }

            # Perform AI-powered data quality analysis
            analysis_result = self.analyze_data_quality_with_ai(df, file_type)

            if not analysis_result["success"]:
                return {
                    "success": False,
                    "message": f"AI data analysis failed: {analysis_result.get('error', 'Unknown error')}",
                    "file_type": file_type,
                    "ai_analysis": analysis_result,
                    "agent_report": self.generate_detailed_report()
                }

            # Process AI analysis results
            ai_analysis = analysis_result.get("structured_response")
            raw_analysis = analysis_result.get("raw_response", "")

            if ai_analysis and isinstance(ai_analysis, dict):
                validation_passed = ai_analysis.get("validation_passed", False)
                quality_score = ai_analysis.get("overall_quality_score", 0.0)
                critical_issues = ai_analysis.get("critical_issues", [])
                warnings = ai_analysis.get("warnings", [])
                recommendations = ai_analysis.get("recommendations", [])

                if validation_passed and quality_score >= 0.7:
                    return {
                        "success": True,
                        "message": f"AI Data validation passed for {file_type.upper()} file. Quality score: {quality_score:.2f}",
                        "file_type": file_type,
                        "rows_validated": len(df),
                        "quality_score": quality_score,
                        "warnings": warnings,
                        "recommendations": recommendations,
                        "ai_analysis": ai_analysis,
                        "agent_report": self.generate_detailed_report()
                    }
                else:
                    # Validation failed or low quality score
                    return {
                        "success": False,
                        "message": f"AI Data validation failed for {file_type.upper()} file. Quality score: {quality_score:.2f}",
                        "file_type": file_type,
                        "rows_checked": len(df),
                        "quality_score": quality_score,
                        "errors": critical_issues,
                        "warnings": warnings,
                        "recommendations": recommendations,
                        "detailed_findings": ai_analysis.get("detailed_findings", ""),
                        "ai_analysis": ai_analysis,
                        "agent_report": self.generate_detailed_report()
                    }
            else:
                # Fallback if structured response parsing fails
                return {
                    "success": False,
                    "message": f"Data validation completed but AI response parsing failed. Raw analysis available.",
                    "file_type": file_type,
                    "rows_checked": len(df),
                    "raw_ai_analysis": raw_analysis[:500] + "..." if len(raw_analysis) > 500 else raw_analysis,
                    "ai_analysis": analysis_result,
                    "agent_report": self.generate_detailed_report()
                }

        except Exception as e:
            error_msg = f"AI Data validation error: {str(e)}"
            return {
                "success": False,
                "message": error_msg,
                "file_type": file_type,
                "ai_analysis": {"error": error_msg},
                "agent_report": self.generate_detailed_report()
            }
