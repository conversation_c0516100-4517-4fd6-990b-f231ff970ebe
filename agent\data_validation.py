import pandas as pd

def validate_data(df: pd.DataFrame) -> tuple[bool, list[str]]:
    """
    Validates data according to business rules mapped from Snowflake stored procedures.

    Returns:
        - is_valid: True if all checks pass.
        - errors: List of validation errors found.
    """
    errors = []

    # 1. Check if file is empty
    if df.empty:
        errors.append("Validation failed: File is empty.")
        return False, errors

    # 2. Null or blank Unique ID
    if "UNIQUE_ID" not in df.columns:
        errors.append("Validation failed: UNIQUE_ID column is missing.")
    else:
        null_uids = df["UNIQUE_ID"].isnull() | (df["UNIQUE_ID"].astype(str).str.strip() == "")
        if null_uids.any():
            errors.append(f"Validation failed: {null_uids.sum()} missing/blank UNIQUE_IDs.")

    # 3. Duplicate UNIQUE_ID
    if df["UNIQUE_ID"].duplicated().any():
        dup_count = df["UNIQUE_ID"].duplicated().sum()
        errors.append(f"Validation failed: {dup_count} duplicate UNIQUE_ID values.")

    # 4. Remove rows with all financials zero (simulate cleanup logic)
    financial_fields = [
        "NAV", "OWNERSHIP_PERCENTAGE", "CAPITAL_CALLED", 
        "NO_OF_SHARES", "COMMITTED_CAPITAL"
    ]
    if all(col in df.columns for col in financial_fields):
        mask_all_zero = (df[financial_fields].fillna(0) == 0).all(axis=1)
        if mask_all_zero.any():
            errors.append(f"{mask_all_zero.sum()} rows contain all-zero financials. These should be excluded.")

    # 5. Check if UNIQUE_ID starts with 'TOTAL' (to remove)
    if "UNIQUE_ID" in df.columns:
        starts_total = df["UNIQUE_ID"].astype(str).str.upper().str.startswith("TOTAL")
        if starts_total.any():
            errors.append(f"{starts_total.sum()} rows have UNIQUE_IDs starting with 'TOTAL'. Should be excluded.")

    is_valid = len(errors) == 0
    return is_valid, errors
