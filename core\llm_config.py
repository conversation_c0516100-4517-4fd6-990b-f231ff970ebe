import os
from dotenv import load_dotenv
from core.dependency_manager import safe_import_langchain, MockLLM

# Load environment variables
load_dotenv()

def get_llm():
    """Get LLM instance with proper error handling and fallbacks"""
    # Check if langchain is available
    langchain_imports = safe_import_langchain()

    if not langchain_imports["available"]:
        print(f"Warning: LangChain not available - {langchain_imports['error']}")
        print("Using mock LLM. Install dependencies with: pip install langchain langchain-openai openai")
        return MockLLM()

    try:
        ChatOpenAI = langchain_imports["ChatOpenAI"]

        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            print("Warning: OPENAI_API_KEY not found. Using mock LLM.")
            print("Set your API key in .env file: OPENAI_API_KEY=your_key_here")
            return MockLLM()

        return ChatOpenAI(
            temperature=float(os.getenv("OPENAI_TEMPERATURE", "0.3")),
            model=os.getenv("OPENAI_MODEL", "gpt-4"),
            openai_api_key=api_key,
            max_tokens=int(os.getenv("OPENAI_MAX_TOKENS", "1000"))
        )

    except Exception as e:
        print(f"Warning: Could not initialize ChatOpenAI - {str(e)}")
        print("Using mock LLM as fallback")
        return MockLLM()

def test_llm_connection():
    """Test if LLM connection is working"""
    try:
        llm = get_llm()
        # Simple test message
        from langchain.schema import HumanMessage
        response = llm([HumanMessage(content="Hello, respond with 'OK' if you can see this.")])
        return True, "LLM connection successful"
    except Exception as e:
        return False, f"LLM connection failed: {str(e)}"