#!/usr/bin/env python3
"""
CBRE Validation Agent - Dependency Checker
Run this script to check and install missing dependencies
"""

import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """Main dependency checking routine"""
    print("🔍 CBRE Validation Agent - Dependency Checker")
    print("=" * 50)
    
    try:
        from core.dependency_manager import get_dependency_manager, create_dependency_report
        
        # Get dependency manager
        dm = get_dependency_manager()
        
        # Print detailed report
        print(create_dependency_report())
        
        # Check if basic features can run
        if not dm.can_run_basic_features():
            print("❌ CRITICAL: Required dependencies missing!")
            print("The application cannot run without these packages.")
            print(f"\n💡 Install with: {dm.get_installation_command()}")
            
            # Ask user if they want to install
            try:
                response = input("\n🤔 Would you like to install missing packages now? (y/N): ").lower().strip()
                if response in ['y', 'yes']:
                    print("\n📦 Installing packages...")
                    success, message = dm.install_missing_packages(include_optional=True)
                    if success:
                        print(f"✅ {message}")
                        print("\n🎉 All dependencies installed! You can now run the application.")
                        return True
                    else:
                        print(f"❌ {message}")
                        print("\n💡 Try installing manually:")
                        print(f"   {dm.get_installation_command()}")
                        return False
                else:
                    print("\n⚠️ Skipping installation. Please install manually:")
                    print(f"   {dm.get_installation_command()}")
                    return False
            except KeyboardInterrupt:
                print("\n\n👋 Installation cancelled by user")
                return False
        
        elif not dm.can_run_ai_features():
            print("⚠️ WARNING: AI dependencies missing!")
            print("Basic features will work, but AI agents will use mock responses.")
            print(f"\n💡 For full AI features, install: {dm.get_installation_command()}")
            
            # Ask user if they want to install AI dependencies
            try:
                response = input("\n🤔 Would you like to install AI dependencies now? (y/N): ").lower().strip()
                if response in ['y', 'yes']:
                    print("\n📦 Installing AI packages...")
                    success, message = dm.install_missing_packages(include_optional=True)
                    if success:
                        print(f"✅ {message}")
                        print("\n🎉 AI dependencies installed! Full AI features now available.")
                    else:
                        print(f"❌ {message}")
                        print("\n💡 Try installing manually:")
                        print(f"   {dm.get_installation_command()}")
                else:
                    print("\n⚠️ Continuing with basic features only.")
            except KeyboardInterrupt:
                print("\n\n👋 Cancelled by user")
            
            return True
        
        else:
            print("🎉 All dependencies are installed!")
            print("✅ Basic features: Available")
            print("🤖 AI features: Available")
            print("\n🚀 Ready to run the CBRE Validation Agent!")
            return True
    
    except ImportError as e:
        print(f"❌ CRITICAL ERROR: Cannot import dependency manager")
        print(f"Error: {e}")
        print("\n💡 This usually means core Python packages are missing.")
        print("Try installing basic requirements:")
        print("   pip install pandas pyyaml python-dotenv")
        return False
    
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {e}")
        print("\n💡 Please check your Python environment and try again.")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 Dependency check cancelled by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        sys.exit(1)
