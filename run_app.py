#!/usr/bin/env python3
"""
CBRE Validation Agent Startup Script
Handles environment setup and launches the Streamlit application
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True

def check_dependencies():
    """Check if required packages are installed"""
    required_packages = [
        'streamlit', 'pandas', 'openpyxl', 'python-dotenv', 
        'langchain', 'pyyaml', 'openai'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} - Not installed")
    
    if missing_packages:
        print(f"\n⚠️ Missing packages: {', '.join(missing_packages)}")
        print("Install them with: pip install -r requirements.txt")
        return False
    
    return True

def setup_environment():
    """Set up environment and directories"""
    print("🔧 Setting up environment...")
    
    # Create necessary directories
    directories = [
        "data/uploads",
        "data/generated", 
        "data/final_output",
        "data/logs",
        "data/test"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ Directory: {directory}")
    
    # Check for .env file
    if not os.path.exists('.env'):
        if os.path.exists('.env.template'):
            print("⚠️ .env file not found. Please copy .env.template to .env and configure it.")
            print("   Especially set your OPENAI_API_KEY")
        else:
            print("⚠️ No .env file found. LLM features may not work without OpenAI API key.")
    else:
        print("✅ .env file found")
    
    return True

def run_integration_test():
    """Run integration test to verify system health"""
    print("\n🧪 Running integration test...")
    try:
        import test_integration
        success = test_integration.main()
        if success:
            print("✅ Integration test passed")
            return True
        else:
            print("⚠️ Integration test had some failures")
            return False
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False

def launch_streamlit():
    """Launch the Streamlit application"""
    print("\n🚀 Launching CBRE Validation Agent...")
    print("The application will open in your default web browser.")
    print("If it doesn't open automatically, go to: http://localhost:8501")
    print("\nPress Ctrl+C to stop the application")
    
    try:
        # Launch Streamlit
        cmd = [sys.executable, "-m", "streamlit", "run", "frontend/main_launcher.py"]
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to launch Streamlit: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    
    return True

def main():
    """Main startup routine"""
    print("🏢 CBRE Validation Agent")
    print("=" * 50)
    
    # Check system requirements
    if not check_python_version():
        return False
    
    print("\n📦 Checking dependencies...")
    if not check_dependencies():
        return False
    
    # Setup environment
    print("\n🔧 Environment setup...")
    if not setup_environment():
        return False
    
    # Ask user if they want to run integration test
    print("\n" + "=" * 50)
    run_test = input("🧪 Run integration test before starting? (y/N): ").lower().strip()
    
    if run_test in ['y', 'yes']:
        test_passed = run_integration_test()
        if not test_passed:
            continue_anyway = input("\n⚠️ Continue anyway? (y/N): ").lower().strip()
            if continue_anyway not in ['y', 'yes']:
                print("Startup cancelled.")
                return False
    
    # Launch application
    print("\n" + "=" * 50)
    return launch_streamlit()

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 Startup cancelled by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Startup failed: {e}")
        sys.exit(1)
