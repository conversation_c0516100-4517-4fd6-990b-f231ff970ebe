import streamlit as st
from utils.state import init_session_state
from agents import file_creation
import os

init_session_state()
st.title("Step 3: File Generation")

if not (st.session_state.file_validation_passed and st.session_state.schema_validation_passed):
    st.warning("Validation steps must be passed before file generation.")
    st.stop()

generated_path = file_creation.generate(st.session_state.uploaded_file)
st.session_state.generated_file_path = generated_path
st.session_state.logs.append(f"Generated file at: {generated_path}")

st.success("File successfully created.")
st.write("Preview:")
st.dataframe(file_creation.preview(generated_path))

st.download_button("Download File", open(generated_path, "rb"), file_name=os.path.basename(generated_path))
