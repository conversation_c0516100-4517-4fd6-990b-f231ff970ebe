import streamlit as st
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from frontend.utils.state import init_session_state, add_log
from frontend.components.status_tracker import render_complete_sidebar

# Safe import with dependency checking
try:
    from agent.file_creation import FileCreationAgent
    from core.dependency_manager import get_dependency_manager
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    DEPENDENCIES_AVAILABLE = False
    IMPORT_ERROR = str(e)

# Initialize session state
init_session_state()

# Render sidebar
render_complete_sidebar()

st.title("🔧 Step 3: File Generation")

# Check dependencies first
if not DEPENDENCIES_AVAILABLE:
    st.error("❌ Dependencies Missing")
    st.error(f"Import error: {IMPORT_ERROR}")
    st.markdown("""
    **Required dependencies are not installed. Please install them:**

    ```bash
    pip install -r requirements.txt
    ```
    """)
    st.stop()

# Check prerequisites
if not (st.session_state.file_validation_passed and st.session_state.schema_validation_passed and st.session_state.data_validation_passed):
    st.warning("⚠️ All validation steps must be passed before file generation.")
    st.markdown("Please complete the validation process first.")
    st.stop()

file_path = st.session_state.uploaded_file_path
file_type = st.session_state.detected_file_type or "nav"

st.markdown(f"**Source file:** `{os.path.basename(file_path)}`")
st.markdown(f"**File type:** `{file_type.upper()}`")

# Check AI capabilities
dm = get_dependency_manager()
ai_available = dm.can_run_ai_features()

if not ai_available:
    st.warning("⚠️ AI dependencies missing - using basic cleaning rules")
    st.info("Install AI dependencies for intelligent cleaning: `pip install langchain langchain-openai openai`")

# File generation
try:
    file_creation_agent = FileCreationAgent()
except Exception as e:
    st.error(f"❌ Failed to initialize file creation agent: {str(e)}")
    st.stop()

if st.button("🚀 Generate AI-Cleaned File", type="primary"):
    with st.spinner("Running AI-powered file generation and cleaning..."):
        try:
            # Pass validation results to the AI agent for context
            validation_results = st.session_state.get("validation_results", {})
            result = file_creation_agent.run(file_path, file_type, validation_results)

            if result["success"]:
                st.session_state.generated_file_path = result["output_path"]
                add_log(f"AI-generated cleaned file: {os.path.basename(result['output_path'])}")

                st.success("✅ AI-powered file generation completed!")

                # Display cleaning impact
                cleaning_impact = result.get("cleaning_impact", {})
                if cleaning_impact:
                    col1, col2, col3, col4 = st.columns(4)
                    with col1:
                        st.metric("Original Rows", cleaning_impact.get("original_rows", 0))
                    with col2:
                        st.metric("Cleaned Rows", cleaning_impact.get("cleaned_rows", 0))
                    with col3:
                        st.metric("Rows Removed", cleaning_impact.get("rows_removed", 0))
                    with col4:
                        st.metric("Removal %", f"{cleaning_impact.get('removal_percentage', 0):.1f}%")

                # Display file statistics
                stats = result.get("stats", {})
                if stats and "error" not in stats:
                    st.subheader("📊 File Statistics")
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("Total Columns", stats.get("total_columns", 0))
                    with col2:
                        st.metric("File Size (MB)", stats.get("file_size_mb", 0))
                    with col3:
                        st.metric("Data Quality", "High" if cleaning_impact.get("removal_percentage", 0) > 0 else "Standard")

                # Display AI Analysis
                ai_analysis = result.get("ai_analysis", {})
                if ai_analysis and isinstance(ai_analysis, dict):
                    with st.expander("🤖 AI Cleaning Analysis", expanded=True):
                        st.subheader("🧠 AI Data Cleaning Strategy")

                        # Cleaning Strategy
                        cleaning_strategy = ai_analysis.get("cleaning_strategy", {})
                        if cleaning_strategy:
                            col1, col2, col3 = st.columns(3)
                            with col1:
                                st.write(f"**Approach:** {cleaning_strategy.get('approach', 'Unknown')}")
                            with col2:
                                st.write(f"**Priority:** {cleaning_strategy.get('priority', 'Unknown')}")
                            with col3:
                                st.write(f"**Risk Tolerance:** {cleaning_strategy.get('risk_tolerance', 'Unknown')}")

                        # Quality Assessment
                        quality_assessment = ai_analysis.get("quality_assessment", {})
                        if quality_assessment:
                            st.write("**AI Quality Assessment:**")
                            col1, col2 = st.columns(2)
                            with col1:
                                overall_quality = quality_assessment.get("overall_quality", 0)
                                st.metric("Overall Quality", f"{overall_quality:.2f}", delta=f"{(overall_quality-0.5):.2f}")
                                completeness = quality_assessment.get("completeness_score", 0)
                                st.metric("Completeness", f"{completeness:.2f}")
                            with col2:
                                consistency = quality_assessment.get("consistency_score", 0)
                                st.metric("Consistency", f"{consistency:.2f}")
                                integrity = quality_assessment.get("integrity_score", 0)
                                st.metric("Integrity", f"{integrity:.2f}")

                        # Business Justification
                        business_justification = ai_analysis.get("business_justification", {})
                        if business_justification:
                            st.write("**Business Justification:**")
                            rationale = business_justification.get("cleaning_rationale", "")
                            if rationale:
                                st.write(rationale)

                            compliance_reqs = business_justification.get("compliance_requirements", [])
                            if compliance_reqs:
                                st.write("**Compliance Requirements:**")
                                for req in compliance_reqs:
                                    st.write(f"- {req}")

                        # Cleaning Recommendations
                        cleaning_recs = ai_analysis.get("cleaning_recommendations", {})
                        if cleaning_recs:
                            st.write("**AI Cleaning Recommendations:**")
                            rows_to_remove = cleaning_recs.get("rows_to_remove", {})
                            for category, items in rows_to_remove.items():
                                if items:
                                    st.write(f"- **{category.replace('_', ' ').title()}:** {len(items) if isinstance(items, list) else items}")

                # Show raw AI analysis if structured parsing failed
                raw_ai_analysis = result.get("raw_ai_analysis")
                if raw_ai_analysis:
                    with st.expander("🤖 Raw AI Analysis", expanded=False):
                        st.text_area("AI Analysis", raw_ai_analysis, height=200)

            else:
                st.error(f"❌ AI file generation failed: {result['message']}")
                add_log(f"AI file generation failed: {result['message']}")

                # Show AI analysis even for failures
                ai_analysis = result.get("ai_analysis", {})
                if ai_analysis:
                    st.write("**AI Analysis:**")
                    if isinstance(ai_analysis, dict):
                        st.json(ai_analysis)
                    else:
                        st.write(str(ai_analysis))

        except Exception as e:
            st.error(f"❌ Error during AI file generation: {str(e)}")
            add_log(f"AI file generation error: {str(e)}")

# Display generated file info and preview
if st.session_state.generated_file_path:
    generated_path = st.session_state.generated_file_path

    st.markdown("---")
    st.subheader("📄 Generated File")

    col1, col2 = st.columns(2)
    with col1:
        st.info(f"**File Path:** {generated_path}")
    with col2:
        st.info(f"**File Name:** {os.path.basename(generated_path)}")

    # File preview
    st.subheader("👀 File Preview")
    try:
        preview_df = file_creation_agent.preview(generated_path, rows=10)
        st.dataframe(preview_df, use_container_width=True)

        # Show more rows option
        if len(preview_df) >= 10:
            if st.button("Show More Rows"):
                full_preview = file_creation_agent.preview(generated_path, rows=50)
                st.dataframe(full_preview, use_container_width=True)

    except Exception as e:
        st.error(f"Error loading preview: {str(e)}")

    # Download button
    st.markdown("---")
    try:
        with open(generated_path, "rb") as file:
            st.download_button(
                label="📥 Download Generated File",
                data=file,
                file_name=os.path.basename(generated_path),
                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                type="primary"
            )
    except Exception as e:
        st.error(f"Error preparing download: {str(e)}")

    # AI Data cleaning summary
    st.markdown("---")
    st.subheader("🤖 AI-Powered Data Cleaning Applied")
    st.markdown("""
    The AI agent analyzed your data and applied intelligent cleaning strategies:
    - 🤖 **AI-Analyzed Data Quality**: Comprehensive quality assessment using LLM
    - 🧠 **Intelligent Row Removal**: AI-identified problematic rows based on business context
    - 📊 **Business Rule Compliance**: AI-validated cleaning decisions with justification
    - 🔍 **Pattern Recognition**: AI-detected anomalies and inconsistencies
    - ⚖️ **Risk Assessment**: AI-balanced data integrity vs. quality improvement
    - 📋 **Compliance Alignment**: AI-ensured regulatory and governance requirements
    """)

    # Show traditional rules as fallback
    with st.expander("📋 Traditional Cleaning Rules (Fallback)", expanded=False):
        st.markdown("""
        If AI analysis fails, these traditional rules are applied:
        - Remove rows with UNIQUE_ID starting with 'TOTAL'
        - Remove rows with all financial fields as zero
        - Remove rows with null/blank UNIQUE_ID
        - Remove duplicate UNIQUE_ID entries (kept first occurrence)
        """)

    # Navigation
    st.markdown("---")
    st.success("🎉 File generation completed!")
    st.markdown("Click on **'4. Final Upload'** in the sidebar to proceed.")

else:
    st.info("👆 Click 'Generate AI-Cleaned File' to create the intelligently processed file.")

    # Show what AI will analyze
    st.markdown("### 🤖 AI Data Cleaning Analysis")
    st.markdown("""
    The AI agent will perform comprehensive analysis including:

    **🧠 Intelligent Analysis:**
    - Business context understanding for financial data
    - Pattern recognition and anomaly detection
    - Quality vs. integrity trade-off assessment
    - Compliance and governance alignment

    **📊 Data Quality Assessment:**
    - Completeness, consistency, and integrity scoring
    - Statistical analysis and outlier detection
    - Business rule validation with justification
    - Risk assessment for cleaning decisions

    **🔧 Smart Cleaning Strategy:**
    - Context-aware row removal recommendations
    - Data transformation suggestions
    - Quality improvement prioritization
    - Minimal data loss with maximum quality gain
    """)

    # Show traditional fallback
    with st.expander("📋 Fallback Cleaning Rules", expanded=False):
        st.markdown("""
        If AI analysis is unavailable, traditional rules apply:
        - Remove rows where UNIQUE_ID starts with 'TOTAL'
        - Remove rows with all financial fields as zero
        - Remove rows with null/blank UNIQUE_ID
        - Remove duplicate UNIQUE_ID entries
        """)
