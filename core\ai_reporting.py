"""
AI-Powered Comprehensive Reporting System
Generates detailed reports from AI agent analysis
"""

import json
from typing import Dict, List, Any, Optional
from datetime import datetime
from core.ai_agent_base import AIAgentBase

class AIReportGenerator(AIAgentBase):
    """AI-powered report generation system"""
    
    def __init__(self):
        super().__init__(
            agent_name="AI Report Generator",
            agent_role="Comprehensive analysis report generation specialist"
        )
    
    def generate_comprehensive_report(self, validation_results: Dict, file_info: Dict = None) -> Dict:
        """Generate comprehensive AI-powered validation report"""
        
        system_prompt = """
        You are an expert financial data analyst and report writer specializing in comprehensive 
        validation report generation for CBRE financial data processing.
        
        Your expertise includes:
        1. Financial data analysis interpretation
        2. Risk assessment and compliance reporting
        3. Data quality assessment documentation
        4. Executive summary creation
        5. Technical recommendation formulation
        
        Generate a comprehensive validation report in JSON format:
        {
            "executive_summary": {
                "overall_status": "PASSED|FAILED|WARNING",
                "confidence_level": 0.0-1.0,
                "key_findings": [],
                "critical_issues": [],
                "recommendation_priority": "LOW|MEDIUM|HIGH|CRITICAL"
            },
            "detailed_analysis": {
                "file_validation": {
                    "status": "PASSED|FAILED|WARNING",
                    "quality_score": 0.0-1.0,
                    "findings": [],
                    "recommendations": []
                },
                "schema_validation": {
                    "status": "PASSED|FAILED|WARNING", 
                    "file_type_detected": "NAV|POSITION|UNKNOWN",
                    "schema_compliance": 0.0-1.0,
                    "findings": [],
                    "recommendations": []
                },
                "data_validation": {
                    "status": "PASSED|FAILED|WARNING",
                    "data_quality_score": 0.0-1.0,
                    "business_rule_compliance": 0.0-1.0,
                    "findings": [],
                    "recommendations": []
                }
            },
            "risk_assessment": {
                "overall_risk_level": "LOW|MEDIUM|HIGH|CRITICAL",
                "data_integrity_risk": "LOW|MEDIUM|HIGH|CRITICAL",
                "processing_risk": "LOW|MEDIUM|HIGH|CRITICAL",
                "compliance_risk": "LOW|MEDIUM|HIGH|CRITICAL",
                "risk_factors": []
            },
            "recommendations": {
                "immediate_actions": [],
                "short_term_improvements": [],
                "long_term_strategies": [],
                "best_practices": []
            },
            "technical_details": {
                "processing_statistics": {},
                "performance_metrics": {},
                "system_information": {}
            },
            "compliance_summary": {
                "regulatory_compliance": "COMPLIANT|NON_COMPLIANT|PARTIAL",
                "data_governance": "EXCELLENT|GOOD|FAIR|POOR",
                "audit_trail": "COMPLETE|PARTIAL|INCOMPLETE"
            }
        }
        """
        
        user_prompt = f"""
        Generate a comprehensive validation report based on the following analysis results:
        
        Validation Results:
        {json.dumps(validation_results, indent=2, default=str)}
        
        File Information:
        {json.dumps(file_info or {}, indent=2, default=str)}
        
        Report Requirements:
        1. Provide clear executive summary with actionable insights
        2. Detail findings from each validation stage
        3. Assess risks and compliance implications
        4. Prioritize recommendations by urgency and impact
        5. Include technical details for audit purposes
        6. Ensure report is suitable for both technical and business stakeholders
        
        Focus on:
        - Data quality and integrity assessment
        - Business impact of identified issues
        - Regulatory and compliance considerations
        - Actionable recommendations with clear priorities
        - Risk mitigation strategies
        """
        
        return self.analyze_with_ai(system_prompt, user_prompt, {
            "validation_results": validation_results,
            "file_info": file_info,
            "report_timestamp": datetime.now().isoformat()
        })
    
    def generate_agent_performance_report(self, agent_reports: List[Dict]) -> Dict:
        """Generate report on AI agent performance and insights"""
        
        system_prompt = """
        You are an AI system performance analyst specializing in multi-agent validation systems.
        
        Analyze the performance and insights from multiple AI agents and provide a comprehensive
        performance report in JSON format:
        {
            "system_performance": {
                "overall_efficiency": 0.0-1.0,
                "agent_coordination": "EXCELLENT|GOOD|FAIR|POOR",
                "processing_time_assessment": "OPTIMAL|ACCEPTABLE|SLOW",
                "accuracy_assessment": "HIGH|MEDIUM|LOW"
            },
            "agent_analysis": {
                "file_validation_agent": {
                    "performance_score": 0.0-1.0,
                    "key_insights": [],
                    "improvement_areas": []
                },
                "schema_validation_agent": {
                    "performance_score": 0.0-1.0,
                    "key_insights": [],
                    "improvement_areas": []
                },
                "data_validation_agent": {
                    "performance_score": 0.0-1.0,
                    "key_insights": [],
                    "improvement_areas": []
                }
            },
            "insights_summary": {
                "most_valuable_insights": [],
                "common_patterns": [],
                "recurring_issues": []
            },
            "system_recommendations": {
                "agent_optimization": [],
                "workflow_improvements": [],
                "monitoring_enhancements": []
            }
        }
        """
        
        user_prompt = f"""
        Analyze the performance and insights from these AI validation agents:
        
        Agent Reports:
        {json.dumps(agent_reports, indent=2, default=str)}
        
        Provide analysis on:
        1. Individual agent performance and effectiveness
        2. Quality and value of insights generated
        3. Agent coordination and workflow efficiency
        4. Common patterns and recurring themes
        5. System-wide improvement opportunities
        6. Recommendations for optimization
        """
        
        return self.analyze_with_ai(system_prompt, user_prompt, {
            "agent_reports": agent_reports,
            "analysis_timestamp": datetime.now().isoformat()
        })
    
    def create_executive_dashboard_data(self, comprehensive_report: Dict) -> Dict:
        """Create data structure for executive dashboard visualization"""
        
        try:
            report_data = comprehensive_report.get("structured_response", {})
            
            if not report_data:
                return {"error": "No structured report data available"}
            
            dashboard_data = {
                "summary_metrics": {
                    "overall_status": report_data.get("executive_summary", {}).get("overall_status", "UNKNOWN"),
                    "confidence_level": report_data.get("executive_summary", {}).get("confidence_level", 0.0),
                    "recommendation_priority": report_data.get("executive_summary", {}).get("recommendation_priority", "UNKNOWN")
                },
                "validation_scores": {
                    "file_quality": report_data.get("detailed_analysis", {}).get("file_validation", {}).get("quality_score", 0.0),
                    "schema_compliance": report_data.get("detailed_analysis", {}).get("schema_validation", {}).get("schema_compliance", 0.0),
                    "data_quality": report_data.get("detailed_analysis", {}).get("data_validation", {}).get("data_quality_score", 0.0)
                },
                "risk_levels": {
                    "overall_risk": report_data.get("risk_assessment", {}).get("overall_risk_level", "UNKNOWN"),
                    "data_integrity_risk": report_data.get("risk_assessment", {}).get("data_integrity_risk", "UNKNOWN"),
                    "processing_risk": report_data.get("risk_assessment", {}).get("processing_risk", "UNKNOWN"),
                    "compliance_risk": report_data.get("risk_assessment", {}).get("compliance_risk", "UNKNOWN")
                },
                "key_insights": report_data.get("executive_summary", {}).get("key_findings", []),
                "critical_issues": report_data.get("executive_summary", {}).get("critical_issues", []),
                "top_recommendations": report_data.get("recommendations", {}).get("immediate_actions", [])
            }
            
            return dashboard_data
            
        except Exception as e:
            return {"error": f"Dashboard data creation failed: {str(e)}"}
    
    def format_report_for_display(self, report: Dict, format_type: str = "html") -> str:
        """Format comprehensive report for display"""
        
        if format_type == "html":
            return self._format_html_report(report)
        elif format_type == "markdown":
            return self._format_markdown_report(report)
        else:
            return json.dumps(report, indent=2, default=str)
    
    def _format_html_report(self, report: Dict) -> str:
        """Format report as HTML"""
        # Basic HTML formatting - can be enhanced
        html = "<div class='ai-validation-report'>"
        html += f"<h1>AI Validation Report</h1>"
        html += f"<p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>"
        html += f"<pre>{json.dumps(report, indent=2, default=str)}</pre>"
        html += "</div>"
        return html
    
    def _format_markdown_report(self, report: Dict) -> str:
        """Format report as Markdown"""
        md = "# AI Validation Report\n\n"
        md += f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        md += "## Report Data\n\n"
        md += f"```json\n{json.dumps(report, indent=2, default=str)}\n```\n"
        return md
