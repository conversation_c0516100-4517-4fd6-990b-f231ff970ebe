from agent.file_validation import FileValidationAgent
from agent.schema_validation import SchemaValidationAgent
from agent.data_validation import DataValidationAgent
from agent.file_upload import FileUploadAgent
 
class MasterAgent:
    def __init__(self):
        self.file_validator = FileValidationAgent()
        self.schema_validator = SchemaValidationAgent()
        self.data_validator = DataValidationAgent()
        self.file_uploader = FileUploadAgent()
 
    def run(self, file_path: str, user_confirmation: str = None):
        # 1. File Validation
        file_check = self.file_validator.run(file_path)
        if not file_check["success"]:
            return {
                "status": "error",
                "stage": "file_validation",
                "message": file_check["message"]
            }
 
        # 2. Schema Validation
        schema_check = self.schema_validator.run(file_path)
        if not schema_check["success"]:
            # Check if rollback scenario (same columns, different names)
            if schema_check["rollback"]:
                if user_confirmation == "proceed":
                    pass  # continue to next agent
                elif user_confirmation == "discard":
                    return {
                        "status": "rollback",
                        "stage": "schema_validation",
                        "message": "User chose to discard due to schema mismatch."
                    }
                else:
                    return {
                        "status": "rollback",
                        "stage": "schema_validation",
                        "message": schema_check["message"]
                    }
            else:
                return {
                    "status": "error",
                    "stage": "schema_validation",
                    "message": schema_check["message"]
                }
 
       
        data_check = self.data_validator.run(file_path)
        if not data_check["success"]:
            return {
                "status": "error",
                "stage": "data_validation",
                "message": data_check["message"]
            }
 
        # 4. File Upload
        upload_result = self.file_uploader.run(file_path)
        if not upload_result["success"]:
            return {
                "status": "error",
                "stage": "upload",
                "message": upload_result["message"]
            }
 
        return {
            "status": "success",
            "stage": "completed",
            "message": "File uploaded successfully after all validations."
        }