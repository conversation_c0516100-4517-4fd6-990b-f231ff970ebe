from agent.file_validation import FileValidationAgent
from agent.schema_validation import SchemaValidationAgent
from agent.data_validation import DataValidationAgent
from agent.file_upload import FileUploadAgent
from core.config_loader import ensure_directories_exist
from datetime import datetime

class MasterAgent:
    def __init__(self):
        # Ensure required directories exist
        ensure_directories_exist()

        # Initialize agents
        self.file_validator = FileValidationAgent()
        self.schema_validator = SchemaValidationAgent()
        self.data_validator = DataValidationAgent()
        self.file_uploader = FileUploadAgent()

    def orchestrate_ai_analysis(self, file_path: str) -> Dict:
        """AI-powered orchestration analysis of the validation process"""

        system_prompt = """
        You are the Master AI Orchestration Agent for CBRE financial data validation.

        Your role is to:
        1. Coordinate multiple AI validation agents
        2. Analyze the overall validation workflow
        3. Provide strategic insights on the validation process
        4. Identify optimization opportunities
        5. Ensure comprehensive coverage of all validation aspects

        Provide orchestration analysis in JSON format:
        {
            "orchestration_strategy": {
                "validation_approach": "comprehensive|targeted|expedited",
                "risk_level_assessment": "low|medium|high|critical",
                "processing_priority": "standard|high|urgent",
                "quality_requirements": "basic|standard|enhanced|premium"
            },
            "agent_coordination": {
                "file_validation_focus": [],
                "schema_validation_focus": [],
                "data_validation_focus": [],
                "cross_agent_insights": []
            },
            "workflow_optimization": {
                "parallel_processing_opportunities": [],
                "sequential_dependencies": [],
                "performance_recommendations": []
            },
            "success_criteria": {
                "minimum_quality_thresholds": {},
                "critical_validation_points": [],
                "acceptable_risk_levels": {}
            }
        }
        """

        user_prompt = f"""
        Analyze and orchestrate the AI validation process for this file:

        File: {file_path}

        Provide strategic orchestration guidance for:
        1. How should the AI agents coordinate their analysis?
        2. What are the key focus areas for each agent?
        3. What quality thresholds should be applied?
        4. How can the validation process be optimized?
        5. What are the success criteria for this validation?

        Consider the file characteristics and business requirements for optimal validation strategy.
        """

        return self.analyze_with_ai(system_prompt, user_prompt, {
            "file_path": file_path,
            "orchestration_timestamp": datetime.now().isoformat()
        })

    def run(self, file_path: str, user_confirmation: str = None):
        """
        Run the complete AI-powered validation pipeline with orchestration

        Args:
            file_path: Path to the file to validate
            user_confirmation: User response for rollback scenarios ("proceed" or "discard")

        Returns:
            dict: Comprehensive result with AI analysis, agent reports, and recommendations
        """
        # Start orchestration
        orchestration_start = datetime.now()

        # AI Orchestration Analysis
        orchestration_result = self.orchestrate_ai_analysis(file_path)

        results = {
            "orchestration_analysis": orchestration_result,
            "file_validation": None,
            "schema_validation": None,
            "data_validation": None,
            "upload": None,
            "agent_reports": [],
            "comprehensive_report": None
        }

        # 1. AI-Powered File Validation
        self.log_interaction("Starting AI File Validation", f"Analyzing file: {file_path}")
        file_check = self.file_validator.run(file_path)
        results["file_validation"] = file_check
        results["agent_reports"].append(file_check.get("agent_report", {}))

        if not file_check["success"]:
            # Generate comprehensive report even for failures
            comprehensive_report = self.report_generator.generate_comprehensive_report(
                results,
                file_check.get("file_info", {})
            )
            results["comprehensive_report"] = comprehensive_report

            return {
                "status": "error",
                "stage": "file_validation",
                "message": file_check["message"],
                "ai_analysis": file_check.get("ai_analysis", {}),
                "results": results,
                "processing_time": (datetime.now() - orchestration_start).total_seconds()
            }

        # 2. AI-Powered Schema Validation
        self.log_interaction("Starting AI Schema Validation", f"Analyzing schema for: {file_path}")
        schema_check = self.schema_validator.run(file_path)
        results["schema_validation"] = schema_check
        results["agent_reports"].append(schema_check.get("agent_report", {}))

        if not schema_check["success"]:
            # Check if rollback scenario (same columns, different names)
            if schema_check.get("rollback", False):
                if user_confirmation == "proceed":
                    self.log_interaction("Rollback Scenario - User Proceeded", "Continuing with validation despite schema mismatch")
                    pass  # continue to next agent
                elif user_confirmation == "discard":
                    comprehensive_report = self.report_generator.generate_comprehensive_report(
                        results,
                        file_check.get("file_info", {})
                    )
                    results["comprehensive_report"] = comprehensive_report

                    return {
                        "status": "rollback",
                        "stage": "schema_validation",
                        "message": "User chose to discard due to schema mismatch.",
                        "ai_analysis": schema_check.get("ai_analysis", {}),
                        "results": results,
                        "processing_time": (datetime.now() - orchestration_start).total_seconds()
                    }
                else:
                    comprehensive_report = self.report_generator.generate_comprehensive_report(
                        results,
                        file_check.get("file_info", {})
                    )
                    results["comprehensive_report"] = comprehensive_report

                    return {
                        "status": "rollback",
                        "stage": "schema_validation",
                        "message": schema_check["message"],
                        "rollback_info": {
                            "expected_columns": schema_check.get("expected_columns", []),
                            "actual_columns": schema_check.get("actual_columns", []),
                            "file_type": schema_check.get("file_type", "unknown")
                        },
                        "ai_analysis": schema_check.get("ai_analysis", {}),
                        "results": results,
                        "processing_time": (datetime.now() - orchestration_start).total_seconds()
                    }
            else:
                comprehensive_report = self.report_generator.generate_comprehensive_report(
                    results,
                    file_check.get("file_info", {})
                )
                results["comprehensive_report"] = comprehensive_report

                return {
                    "status": "error",
                    "stage": "schema_validation",
                    "message": schema_check["message"],
                    "ai_analysis": schema_check.get("ai_analysis", {}),
                    "results": results,
                    "processing_time": (datetime.now() - orchestration_start).total_seconds()
                }

        # Get file type from schema validation for data validation
        file_type = schema_check.get("file_type", "nav")

        # 3. AI-Powered Data Validation
        self.log_interaction("Starting AI Data Validation", f"Analyzing data quality for {file_type.upper()} file")
        data_check = self.data_validator.run(file_path, file_type)
        results["data_validation"] = data_check
        results["agent_reports"].append(data_check.get("agent_report", {}))

        if not data_check["success"]:
            comprehensive_report = self.report_generator.generate_comprehensive_report(
                results,
                file_check.get("file_info", {})
            )
            results["comprehensive_report"] = comprehensive_report

            return {
                "status": "error",
                "stage": "data_validation",
                "message": data_check["message"],
                "ai_analysis": data_check.get("ai_analysis", {}),
                "results": results,
                "processing_time": (datetime.now() - orchestration_start).total_seconds()
            }

        # 4. File Upload (Traditional - not AI-powered)
        self.log_interaction("Starting File Upload", f"Uploading validated {file_type.upper()} file")
        upload_result = self.file_uploader.run(file_path)
        results["upload"] = upload_result

        if not upload_result["success"]:
            comprehensive_report = self.report_generator.generate_comprehensive_report(
                results,
                file_check.get("file_info", {})
            )
            results["comprehensive_report"] = comprehensive_report

            return {
                "status": "error",
                "stage": "upload",
                "message": upload_result["message"],
                "results": results,
                "processing_time": (datetime.now() - orchestration_start).total_seconds()
            }

        # 5. Generate Comprehensive AI Report
        self.log_interaction("Generating Comprehensive AI Report", "Compiling all AI agent analyses")
        comprehensive_report = self.report_generator.generate_comprehensive_report(
            results,
            file_check.get("file_info", {})
        )
        results["comprehensive_report"] = comprehensive_report

        # 6. Generate Agent Performance Report
        agent_performance_report = self.report_generator.generate_agent_performance_report(
            results["agent_reports"]
        )
        results["agent_performance_report"] = agent_performance_report

        processing_time = (datetime.now() - orchestration_start).total_seconds()
        self.log_interaction("Validation Pipeline Completed", f"Total processing time: {processing_time:.2f} seconds")

        return {
            "status": "success",
            "stage": "completed",
            "message": f"AI-powered validation completed successfully. File type: {file_type.upper()}",
            "file_type": file_type,
            "final_destination": upload_result.get("destination"),
            "processing_time": processing_time,
            "ai_insights": {
                "orchestration_analysis": orchestration_result,
                "comprehensive_report": comprehensive_report,
                "agent_performance": agent_performance_report
            },
            "results": results,
            "master_agent_report": self.generate_detailed_report()
        }